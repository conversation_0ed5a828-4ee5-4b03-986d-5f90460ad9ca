ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp.dll:
    - AddObjects
    - BackgroundChanger
    - <PERSON>Zoom
    - Change<PERSON>hara<PERSON>
    - CrashlyticsTester
    - CustomCrashReporter
    - FlutterUnityIntegration.UnityMessageManager
    - NumberActions
    - RotateObject
    - ScrollAndPinch
    - Version3.PinchAndZoom
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.TextMeshPro:
    - TMPro.TMP_ColorGradient
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    - TMPro.TMP_InputField
    - TMPro.TextMeshProUGUI
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.StandaloneInputModule
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.RectMask2D
    - UnityEngine.UI.Text
  serializedClasses:
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.TMP_GlyphAdjustmentRecord
    - TMPro.TMP_GlyphPairAdjustmentRecord
    - TMPro.TMP_GlyphValueRecord
    - TMPro.TMP_InputField/OnChangeEvent
    - TMPro.TMP_InputField/SelectionEvent
    - TMPro.TMP_InputField/SubmitEvent
    - TMPro.TMP_InputField/TextSelectionEvent
    - TMPro.TMP_InputField/TouchScreenKeyboardEvent
    - TMPro.TMP_Sprite
    - TMPro.TMP_SpriteCharacter
    - TMPro.TMP_SpriteGlyph
    - TMPro.TMP_Style
    - TMPro.VertexGradient
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    UnityEngine.UI:
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.FontData
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.SpriteState
  methodsToPreserve:
  - assembly: Assembly-CSharp
    fullTypeName: NumberActions
    methodName: NumberFiveOneFourNine
  - assembly: Assembly-CSharp
    fullTypeName: BackgroundChanger
    methodName: BackgroundImageChanger
  - assembly: Assembly-CSharp
    fullTypeName: NumberActions
    methodName: ButtonClicked
  - assembly: Assembly-CSharp
    fullTypeName: NumberActions
    methodName: ButtonClicked
  - assembly: Assembly-CSharp
    fullTypeName: NumberActions
    methodName: ButtonClicked
  - assembly: Assembly-CSharp
    fullTypeName: ChangeCharacter
    methodName: CharacterChanger
  - assembly: Assembly-CSharp
    fullTypeName: AddObjects
    methodName: AddHeadDressToCharacter
  - assembly: Assembly-CSharp
    fullTypeName: NumberActions
    methodName: ButtonClicked
  - assembly: Assembly-CSharp
    fullTypeName: NumberActions
    methodName: AnyGivenNumber
  sceneClasses:
    Assets/Scenes/3Dcharcterbtnclick Scene.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16648}
    - Class: 114
      Script: {instanceID: 16650}
    - Class: 114
      Script: {instanceID: 16696}
    - Class: 114
      Script: {instanceID: 16828}
    - Class: 114
      Script: {instanceID: 17088}
    - Class: 114
      Script: {instanceID: 17338}
    - Class: 114
      Script: {instanceID: 17464}
    - Class: 114
      Script: {instanceID: 17822}
    - Class: 114
      Script: {instanceID: 18036}
    - Class: 114
      Script: {instanceID: 18356}
    - Class: 114
      Script: {instanceID: 18930}
    - Class: 114
      Script: {instanceID: 19120}
    - Class: 114
      Script: {instanceID: 19370}
    - Class: 114
      Script: {instanceID: 19478}
    - Class: 114
      Script: {instanceID: 19492}
    - Class: 114
      Script: {instanceID: 19532}
    - Class: 114
      Script: {instanceID: 19536}
    - Class: 114
      Script: {instanceID: 19774}
    - Class: 114
      Script: {instanceID: 20348}
    - Class: 114
      Script: {instanceID: 20558}
    - Class: 114
      Script: {instanceID: 20836}
    - Class: 114
      Script: {instanceID: 20920}
    - Class: 114
      Script: {instanceID: 21248}
    - Class: 114
      Script: {instanceID: 21338}
    - Class: 114
      Script: {instanceID: 21920}
    - Class: 114
      Script: {instanceID: 22784}
    - Class: 114
      Script: {instanceID: 23386}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: d41ab9b5ea92474f9c0a6937a0607ce1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 9104447b683bf70406fa01f12ecb564a
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: 540882762544409cc4530e60db00e951
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: 55060a7bf226ac385ce19ebd450f199f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: dd9dda1dfd39e605c49e72003342ef5d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: 0f19765290d81f9e93eff7828a091d40
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: f45600bcc886206dd55a3208474aad61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: a3901985eb076c1c9b7562fca82ca3e0
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 8dff8e9853297d341afa0c11f9091b59
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: 00bbd9db8634496d079d17711b2ff7c4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: d0caa48b5fe6778ce3f0de7aec45c4e6
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: SkewTextExample
  - hash:
      serializedVersion: 2
      Hash: 157d3cf40178d244cd11ae1c9791ead7
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: ed8e62f07ec878233f5493a335003493
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: 8703b6ae2b0eefd9dafbc44d8d333fb1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: f778e23b6ff26b8de4721be0eb1fa240
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: f854af586011dd194574f89546642e29
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 11a492830b189fafb9ed2f7afa464c56
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: SimpleScript
  - hash:
      serializedVersion: 2
      Hash: 3c29ec925d6bdcdaf064816bca7d3ea9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ChangeCharacter
  - hash:
      serializedVersion: 2
      Hash: b12640dfdb5966813e9dd838893e6c4a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0c6de72eaf8c1070da824268c4bfad9d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: ad49e20f31ed572e586aa8bbaafb591b
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1bc6ff8d02d2d68019aa6a028f8d409d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: b40f8ffcaa6b71545b8d6ff04b849814
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: 7e93247d600e115dac0cdd880de94e99
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: 83cfda82082c233d96cc677c19f57e07
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: 671b3a8126217e45a3d807ac1f4e0c47
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CrashlyticsTester
  - hash:
      serializedVersion: 2
      Hash: 1ab136900c0edaeeb49424c852595030
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: bc424b2a2d87ceeb0740af0a41a0d0ad
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SceneLoader
  - hash:
      serializedVersion: 2
      Hash: 1321993db8249e1e05e1e0492b2bbdc3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: c9b1ea3bf987a2e780a24849503ca72e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark01_UGUI
  - hash:
      serializedVersion: 2
      Hash: e987953190eb4c12cf19df790566dc2c
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: 52db48e3be0a2b4f8b06f918fd1ebfbc
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: f295412bc00b58b9095a71e9764cc886
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: 04b6685b6f724c0d6024e6b6f96c8202
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: ada873da50974d1e7ce247ffb296b0f3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 7e12302f66e3c8ed5bcfea4ce65fccea
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: ed8bac09ffacadca9994789e381c69ce
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextSelector_A
  - hash:
      serializedVersion: 2
      Hash: fce9b835d1e8b08e7ecea8da19f21986
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: 6ddf94363c6ce4b02d9fdd51290cb0f9
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: 5a4aa2850fe11587f47e34c270c7fd22
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: 3b5263a98bcc71d9a9d1d78a31b34ced
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 28fd3d045acc780719a17f02073eb448
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 8e6dc3d79712ca4934f6d50c80f010b1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: 6d394de691f6d0187f61c08889353d0e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: e23d2210701e34e6850023cabe554c1f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: bf40c7a9dc15bc219687c9838aa9b41f
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: b98001682cba83786775c9e415b89a61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: b838cbada0b03f1cfbaebc8e124f4f39
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: 1d830bc5cfaaf24301dffea2877f4bea
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexShakeA
  - hash:
      serializedVersion: 2
      Hash: a1ac4c50e55874de16eee3db71aa9784
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: 915204dac8acad99f076d6e72ada0a0f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: 593a641cd7822a10b9a54aa9eb5c3d5e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark04
  - hash:
      serializedVersion: 2
      Hash: d90629714fa6b0145fcaffc87f5c6559
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: bb8d968ef4474e71aaad4a4dce279f90
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 55ebd997e12c940f93288f5a9bdb91c7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PinchZoom
  - hash:
      serializedVersion: 2
      Hash: b6307dfc54cca9ba44f47185c578b0ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: d7c8092511063e0dae8106bfc8ed63dd
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: 715f7928b39881049ce8c46350cb8681
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformParentChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 86344100c716167fb86137c127ca9eee
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: c6369b4cc3be620d6c9300e920bc3d52
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: 7bdff9e91edfd9600f92a185def7e34a
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: 43ebd945b04bced376c791411cf5a289
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5b8b7230ad968399f6726aa1269dcdcb
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: d3bece132a2b48e63d78047dece48ba7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ChatController
  - hash:
      serializedVersion: 2
      Hash: cf32d98954e63b8e30e9334b152e9b8e
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: e47d77dff9f7fd0f54480d10a98b411f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GameManager
  - hash:
      serializedVersion: 2
      Hash: 418b67019f13f9684552f2693bae3971
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextMeshProFloatingText
  - hash:
      serializedVersion: 2
      Hash: e6f8333cc8a800e2866870a5ef7efc35
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 960935b102b3064f4b924de16426e340
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: b2d2a01a2a85d9ed12e80cb33a9a6044
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 6217b1287eca7eed313cf0b864249a30
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20839b8a7ade3b2d57f46175f0c94a0a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BackgroundChanger
  - hash:
      serializedVersion: 2
      Hash: 0a3f328f3ea906bbbcf794f4c27ee59d
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 42284ecb06b05bfc619be3758fa5dd7a
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: 72561d2e822b466169cd12a3b9f9ae0c
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexShakeB
  - hash:
      serializedVersion: 2
      Hash: fb79e95da8891fc7ac6c36ca6967af23
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 552dca338f20933ce362ebbe559a6aa6
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: c4df274da8e6cb6b24e0f1f3fab23669
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CustomCrashReporter
  - hash:
      serializedVersion: 2
      Hash: 1deda86fe7ffd8587afcfe68a02d034c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: EnvMapAnimator
  - hash:
      serializedVersion: 2
      Hash: 95426d463074947dcbffc797c44e779c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: StandaloneInputModuleModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 665da785348a8806bf1524fe10742a60
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_UiFrameRateCounter
  - hash:
      serializedVersion: 2
      Hash: 5835aa044b7ea3413cc6d9d819e95dd9
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: b747f5d40674055dd70e18552f1b0447
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: 164a37e0bc379311785dd54beac44331
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 32bfadf4ab14c1e4298d945ac7dbee8f
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 0bc7ef1b7516f21320ec49bfd31eef2e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: 4718916586b3818eb69bf65df61f0f3a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 185e7bacfbbc5defefa609c70f7ff4ce
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 682dfc653b22feea53b38adb3bc66414
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: c78ed13f6b5a632d70d4f73f56ad81f2
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 7053e305cdc1eb560f4c3be49d3b5136
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 042785b832afb73fd0585521aa7baf43
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 1a680d9a08de79b0d2c779e78c9843b4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: c933b590b3badb5918fa01e73cd5cc0c
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: ca7965796a6fc0e5baad4cb19f5b5ded
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: e416782d95a812c5b5b00e9b103a8e94
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: CameraController
  - hash:
      serializedVersion: 2
      Hash: 2c172fcb725ba1088d5d5656a3672c56
    assemblyName: Assembly-CSharp
    namespaceName: Version3
    className: PinchAndZoom
  - hash:
      serializedVersion: 2
      Hash: 0c9772970f5fb4758b65e5437b5d9a90
    assemblyName: Assembly-CSharp
    namespaceName: FlutterUnityIntegration
    className: UnityMessageManager
  - hash:
      serializedVersion: 2
      Hash: 9ebc0a3016506a0cbb7e0306dfcb9497
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: e7e95903ca66d58e1a7cbd71f824e16d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 215d2dc6ec6ea06728398ea39a103cb3
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: 9078325c2a6f6a2d9612ade897511860
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: 96e7bcfd2d072992dab64ac44058b930
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 52840a82fb83b3a7df342e354c4815e4
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMPro_InstructionOverlay
  - hash:
      serializedVersion: 2
      Hash: a75a568ce902366f4e82fc98ca6108f5
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_ExampleScript_01
  - hash:
      serializedVersion: 2
      Hash: dc31d211c7459841fe4f406ae2e51480
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextConsoleSimulator
  - hash:
      serializedVersion: 2
      Hash: ea1b87a3bfed09792e7720338241c7a0
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: ff71db1a48de41d3a8a5ffdfae1ea607
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 5a1710e1ce9ddf5eb06e0d2693171f49
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: ObjectSpin
  - hash:
      serializedVersion: 2
      Hash: 7e9cad7f71f9cc52f699bbd2d2a75734
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: aa349b22cd61b9e8488d736f82590d6b
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextInfoDebugTool
  - hash:
      serializedVersion: 2
      Hash: 0b63f6d3335e11939ce8790b904c8691
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: e402a382f213255c22bda9254dd831b3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: e88d71ef93aba4e7faf243430f6a10ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: 8dc40a01667ca02b3354a4e7a61be082
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 90cf308600fb4aeb677786843453ec55
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: ceeb6c51b89f022a3deb7b3408c66745
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextEventCheck
  - hash:
      serializedVersion: 2
      Hash: f2098fcd8ee983a05ec192f63904298f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: db9b308769d19e1f9e161df0392c23ca
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformChildrenChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6d94138b60a9299798dda96c1ee6725f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 51b80d3734ea86b38e3101db98029e15
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 0370b9f95798139b666659c7e1be6147
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: 47ee34eb8b6021efa0289dbbb17a5a85
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: ec6cf673540b2d4e42c612611eeafe4d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: ab3d19695db8289414a06e6d45fd6c8e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: ShaderPropAnimator
  - hash:
      serializedVersion: 2
      Hash: 10c17b309bdca62b4d7fbaf113ed4ca0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 522f6865b4ec12522e602206785309f8
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 53e329840e6d03aebec76623c3b054db
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 33f160a5c07e580c29aba2bd1e4db811
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 191cd500c627cd6682ef6f70b8e47f2c
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0c0b64d103fe555d830e8ca206abd12a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1981decccddfde3e97fcfd6689332a6f
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: WarpTextExample
  - hash:
      serializedVersion: 2
      Hash: 17f82e3e7d8f8b07a270a4aad9bfe79d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 0dd14e314956b0d0d83ffcffe60aa46f
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_DigitValidator
  - hash:
      serializedVersion: 2
      Hash: d82086405f46de14829f6b4bcbdd8fc9
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5874b789cd9832847c61ebfa803213af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: 9dd54ce33440072d6692a67e0c384ad1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: bf0842d88681628b29beb00b5e2ab785
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: b5007673bbbdb9d12597a1d8201370f5
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: 9c2d6b5b28d233aacec6eb0c9f38e0d6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AddObjects
  - hash:
      serializedVersion: 2
      Hash: 2071f7ac454ae14eafecfd4041edae3a
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 1ce63d97d1ca25cba2d91d5f32fd5f79
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 6cf63e9dc888f92a3672d2e4db631c8e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: 043e2772a038ea5aae739a54b296cc21
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Rotate
  - hash:
      serializedVersion: 2
      Hash: 35e0b31791c377513b6fc7b9568c50cd
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark02
  - hash:
      serializedVersion: 2
      Hash: 4166580c620718548959a77fa7b43fc1
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TextMeshSpawner
  - hash:
      serializedVersion: 2
      Hash: c137893892e9e3d3a7fca5da39b89112
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CameraZoom
  - hash:
      serializedVersion: 2
      Hash: ef38c1407dba8c1f5d69f5db61a694fe
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: 33656f83bb0079184b2206a69690ec46
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: 9637dcdf3068a91dfe653e61bb82f13d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: da5a949a0bbd2ff91bc51c5805c2c41f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: fc0a9a681d29388386a9abf3f08b024a
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark03
  - hash:
      serializedVersion: 2
      Hash: 4f353524ce6564e40764f1ea080b2d85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: a90f6a00b23520788be7d89ee3b0773d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 13f262cc0741566a7ab72682984bc3e1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: febcfbf0a0d85e37808a3740e3c2c6fa
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: 672716ae97f6ddb615fa5ac3ec9da349
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: bc75926bfd3609757f7bf33ff766f026
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: 8ed5daadd237bb82e132790f75ffccaf
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: a92561298b80715aa69e8fa770123cb5
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 8bbce9932f67ad8e7f4eefec87800f94
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: b39dbcbe3a58d3a88338d7345d0cc4f3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RotateObject
  - hash:
      serializedVersion: 2
      Hash: 3074afe1b03a0fb081e176a4ef1b9d09
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: 7fdd3ee28e9042c3ec83463d0bb3b230
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_FrameRateCounter
  - hash:
      serializedVersion: 2
      Hash: b25a96c00e95419cdff2e5b6643b9c9f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: test
  - hash:
      serializedVersion: 2
      Hash: e63fe08659e7e4647a51098c666f8845
    assemblyName: Unity.InputSystem
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: f669be52680c88846b2093fd41d1e06e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20a120e55ad3ab65556328d5fd8309dd
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: 33eb495d99aa9bea4dbe8b4c4e02c7bb
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 17142b03663387290480c82303274c02
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: f2305da2400dfe6f069770a1a63f0f7c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 4878e735441f4041539853759559c86f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: c4b85a67513b4f0cf7b5fb2d5ded04b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: d2ecd94550edf1b56aff07b1f4e41c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8668b70b18eb7f5cd2dce04386848007
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexJitter
  - hash:
      serializedVersion: 2
      Hash: 7bb32d6e4c7614a97d9723ce7e26588c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: BasicPlayableBehaviour
  - hash:
      serializedVersion: 2
      Hash: e8d390bd9ecacc453a7500b90d0c0292
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: 19824dd35debb3fff0983df49f8359ab
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: afb7b65d28d5a7423e69a1b076be0064
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 6a0b5b415c00f783202fd1ae2d556cb2
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b14e186823458f549289789cccbb77ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 04f2d6a4db8a670aeb2c87d9ffd08865
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_TextEventHandler
  - hash:
      serializedVersion: 2
      Hash: ee34255e3e86289bde9bad5e07b84cbc
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 830c3b545f6e6ee60a4cebab90516f2c
    assemblyName: UnityEngine.TestRunner
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: d0cdd713f9bf4e6a625d9e7804deca42
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: b8776581339b7074dbdafc999cee3ecf
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: a89b0448af4e1cd103a77f9fec0a961f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5ebc58ec194dd44630b25fcc270ad4af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: e2734bcc7b3e94776ade5d59f54fec1e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TMP_TextSelector_B
  - hash:
      serializedVersion: 2
      Hash: 4728e3d21cc2a12e391e71ec9b7eed0b
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 67272c9c30c50b68ef1ce72ac0191c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: f5bf95299aebb16f53148a8dab5ff5cf
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: NumberActions
  - hash:
      serializedVersion: 2
      Hash: d0392b7f3f52d5af537ade186b4f51f1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: dbe80c9dc024d18e75107a0b4da06133
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: d5cd7f207e91ca8336c65a577a6fb2a0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: 9a174471d4db2c0c6d3ac47d2f818db1
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: 40be2ad159aa1a2b72ecc74cb38c7824
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 2bbd05175d4cdd21e24f9716cdd24f83
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 0f5a086d4d028363983ba6ca30a01397
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: 2c583ad0ac85d03e8700dd05fdcb46cb
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: 502a0d788634601faaefeacfda55e23a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DropdownSample
  - hash:
      serializedVersion: 2
      Hash: b30c8635462e66ac20f4241106119f77
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: c4de0ff7e075dd89c9fed5913dd775d2
    assemblyName: Assembly-CSharp
    namespaceName: TMPro
    className: TMP_PhoneNumberValidator
  - hash:
      serializedVersion: 2
      Hash: c4ae0ec287bc3cde0bc98e1a65f9cef7
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexColorCycler
  - hash:
      serializedVersion: 2
      Hash: 568292fb7ea6c4f7a667e5ff76bc7e85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 1553f209ae0b2d5d3a35685fca55f9c3
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 9eccc4340fcd994586e0dcb134c4612e
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: TeleType
  - hash:
      serializedVersion: 2
      Hash: dfb00a28e109107e5f45d0b9a1ccadb1
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ScrollAndPinch
  - hash:
      serializedVersion: 2
      Hash: 9fb1c3ab48b256d508ecddd967f98652
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: VertexZoom
  - hash:
      serializedVersion: 2
      Hash: ff76cf6cf52b4db7b95af6ffadca9288
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: c4cdb902228df56d5b2b639d6a6bbd3c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: 02a47340661d2ea7c6d30e292cfef403
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8bf800a764665b085fe469e8aea7f167
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 4fcf0bb4aff1d14a1dfcdedc4b07bd05
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 216f6c07bfa250e44b3e8d62572474e8
    assemblyName: Assembly-CSharp
    namespaceName: TMPro.Examples
    className: Benchmark01
  - hash:
      serializedVersion: 2
      Hash: 00eef92bf387da2c9009267123d9d674
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8b15e35eb98fc258ec2e839df7c3c9b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  platform: 9
  scenePathNames:
  - Assets/Scenes/3Dcharcterbtnclick Scene.unity
  playerPath: /Users/<USER>/Documents/gitlab asl/from gitlab/asl-flutter-app/ios/UnityLibrary
