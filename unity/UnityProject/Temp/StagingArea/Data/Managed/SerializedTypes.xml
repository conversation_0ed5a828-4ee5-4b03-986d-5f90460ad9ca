<linker>
	<assembly fullname="Unity.TextMeshPro">
		<type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.KerningTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_GlyphAdjustmentRecord" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_GlyphPairAdjustmentRecord" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_GlyphValueRecord" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/OnChangeEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/SelectionEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/SubmitEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/TextSelectionEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_InputField/TouchScreenKeyboardEvent" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Sprite" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_SpriteCharacter" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_SpriteGlyph" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.TMP_Style" preserve="nothing" serialized="true"/>
		<type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule">
		<type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.TextCoreFontEngineModule">
		<type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true"/>
		<type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true"/>
	</assembly>
</linker>
