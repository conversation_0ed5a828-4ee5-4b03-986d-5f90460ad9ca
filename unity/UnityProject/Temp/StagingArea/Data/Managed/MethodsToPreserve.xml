<linker>
	<assembly fullname="Assembly-CSharp" ignoreIfMissing="1">
		<type fullname="AddObjects">
			<method name="AddHeadDressToCharacter"/>
		</type>
		<type fullname="BackgroundChanger">
			<method name="BackgroundImageChanger"/>
		</type>
		<type fullname="ChangeCharacter">
			<method name="CharacterChanger"/>
		</type>
		<type fullname="NumberActions">
			<method name="AnyGivenNumber"/>
			<method name="ButtonClicked"/>
			<method name="ButtonClicked"/>
			<method name="ButtonClicked"/>
			<method name="ButtonClicked"/>
			<method name="NumberFiveOneFourNine"/>
		</type>
	</assembly>
</linker>
