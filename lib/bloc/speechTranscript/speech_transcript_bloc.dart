import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:arabic_sign_language/data/service/recoding_service.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:asl_flutter_input/asl_flutter_input.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart';

part 'speech_transcript_event.dart';
part 'speech_transcript_state.dart';

class SpeechTranscriptBloc
    extends Bloc<SpeechTranscriptEvent, SpeechTranscriptState> {
  final RecordingService recordingService;
  final TextConversionService textConversionService;
  final _aslFlutterInputPlugin = AslFlutterInput();
  List<String> rootWords = [];
  int speechTextIndex = 0;
  List<List<String>> speechTexts = [];
  List<List<String>> speechWords = [];

  // Speech recognition properties
  String recognizedWord = "";
  String lastResponse = "";
  String currentResponse = '';
  String previousResult = "";
  String newTextToProcess = "";
  Timer? _speechTimer;
  String lastProcessedText = "";

  Timer? _androidSpeechTimer;
  String lastUpdatedText = '';
  int noResponseCount = 0;
  bool isAnimating = false;
  bool _shouldStopAnimationProcesses = false;

  SpeechTranscriptBloc({
    required this.recordingService,
    required this.textConversionService,
  }) : super(const SpeechTranscriptInitial()) {
    on<StartRecording>(_onStartRecording);
    on<StopRecording>(_onStopRecording);
    on<ResetSpeechTranscription>(_onResetSpeechTranscription);
    on<TranslateButtonTapped>(_onTranslateButtonTapped);
    on<UpdateCurrentAnimationText>(_onUpdateCurrentAnimationText);
    on<StartSpeechRecognition>(_onStartSpeechRecognition);
    on<StopSpeechRecognition>(_onStopSpeechRecognition);
    on<RestartSpeechRecognition>(_onRestartSpeechRecognition);
    on<UploadFileForTranscription>(_onUploadFileForTranscription);
    on<StopAllAnimationProcesses>(_onStopAllAnimationProcesses);
  }

  FutureOr<void> _onStartRecording(
      StartRecording event, Emitter<SpeechTranscriptState> emit) async {
    emit(StartRecordingState(isRecordingEnabled: event.isRecordingEnabled));
    final hasPermission = await event.controller.checkPermission();
    if (hasPermission) {
      await event.controller.record(path: event.path);
    } else {
      emit(const SpeechScreenError(message: "Permission denied"));
    }
  }

  FutureOr<void> _onStopRecording(
      StopRecording event, Emitter<SpeechTranscriptState> emit) async {
    emit(StopRecordingState(
        isRecordingStopped: true,
        isRecordingEnabled: state.isRecordingEnabled));

    await event.controller.pause();
    // if (path?.isNotEmpty == true) {
    //   final recording = await File(event.path).readAsBytes();
    //   final recorderList =
    //       await recordingService.getTranscriptionFromRecording(recording);
    //   print("recorderList => $recorderList");

    //   if (recorderList.isEmpty) {
    //     // emit(const UnityScreenError(
    //     //   message: "Please try again",
    //     //   isSpeechButtonDisabled: false,
    //     //   isVideoButtonDisabled: false,
    //     // ));
    //   } else {
    //     for (var item in recorderList) {
    //       final rootWord = item.root ?? "";
    //       if (rootWord.contains(',')) {
    //         final splitWords = rootWord.split(',');
    //         for (var word in splitWords) {
    //           // emit(SendMessagesToUnity(
    //           //   message: {'root': word, 'word': item.word},
    //           //   currentAnimation: state.currentAnimation,
    //           // ));
    //         }
    //       } else {
    //         // emit(SendMessagesToUnity(
    //         //   message: {'root': item.root, 'word': item.word},
    //         //   currentAnimation: state.currentAnimation,
    //         // ));
    //       }
    //     }
    //   }
    // } else {
    //   emit(const SpeechScreenError(message: "Failed to stop recording"));
    // }
    // event.controller.refresh();
  }

  FutureOr<void> _onResetSpeechTranscription(
      ResetSpeechTranscription event, Emitter<SpeechTranscriptState> emit) {
    event.controller.stop();
    emit(const SpeechTranscriptInitial());
  }

  FutureOr<void> _onTranslateButtonTapped(
      TranslateButtonTapped event, Emitter<SpeechTranscriptState> emit) async {
    final path = await event.controller.stop();
    if (path?.isNotEmpty == true) {
      final recording = await File(event.path).readAsBytes();

      final fileName = path!.split('/').last;
      print("Recording file name: $fileName");

      final recorderList = await recordingService.getTranscriptionFromRecording(
        recording,
        fileName: fileName,
      );
      print("recorderList => $recorderList");

      if (recorderList.isEmpty) {
        emit(const SpeechScreenError(message: "Please try again"));
        // emit(const UnityScreenError(
        //   message: "Please try again",
        //   isSpeechButtonDisabled: false,
        //   isVideoButtonDisabled: false,
        // ));
      } else {
// for (var textItem in texts) {
//         final rootWord = textItem.root;
//         for (var item in rootWord) {
//           rootWords.add(item);
//           if (item.contains(',')) {
//             final splitWords = item.split(',');
//             for (int i = 0; i < splitWords.length; i++) {
//               print(
//                   "Iteration: $i, Word: ${splitWords[i]}, Full List: $splitWords");
//               await Future.delayed(const Duration(milliseconds: 150), () {
//                 emit(SendMessagesToUnity(

        for (var item in recorderList) {
          final rootWord = item.root ?? "";
          // rootWords.add(rootWord);
          if (rootWord.contains(',')) {
            final splitWords = rootWord.split(',');
            for (var word in splitWords) {
              emit(SendMessagesToSpeechScreen(
                  message: {'root': word, 'word': item.word},
                  currentAnimation: state.currentAnimation));
              // emit(SendMessagesToUnity(
              //   message: {'root': word, 'word': item.word},
              //   currentAnimation: state.currentAnimation,
              // ));
            }
          } else {
            emit(SendMessagesToSpeechScreen(
                message: {'root': item.root, 'word': item.word},
                currentAnimation: state.currentAnimation));
            // emit(SendMessagesToUnity(
            //   message: {'root': item.root, 'word': item.word},
            //   currentAnimation: state.currentAnimation,
            // ));
          }
        }
      }
    } else {
      emit(const SpeechScreenError(message: "Failed to stop recording"));
    }
    event.controller.refresh();
  }

  FutureOr<void> _onUpdateCurrentAnimationText(UpdateCurrentAnimationText event,
      Emitter<SpeechTranscriptState> emit) async {
    emit(UpdateCurrentAnimation(
        currentAnimation: event.animationText,
        isRecordingEnabled: state.isRecordingEnabled,
        isTranslateButtonDisabled: state.isTranslateButtonDisabled,
        isDataLoading: state.isDataLoading,
        isRecordingStopped: state.isRecordingStopped,
        animationSpeed: state.animationSpeed));
  }

  FutureOr<void> _onStartSpeechRecognition(
    StartSpeechRecognition event,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    recognizedWord = "";
    lastResponse = "";
    currentResponse = '';
    previousResult = ""; // Store the previous result
    newTextToProcess = "";
    // emit(const StartSpeechToText(
    //   isTextToSpeechStart: true,
    //   isRecorderButtonDisabled: true,
    //   isVideoButtonDisabled: true,
    //   isDictionaryButtonDisabled: true,
    //   isTextFormFieldDisabled: true,
    // ));
    if (Platform.isIOS) {
      // Await initialization
      bool initialized = await event.speechToText.initialize(
        onError: (errorNotification) {
          recognizedWord = "";
        },
        onStatus: (status) async {
          if (Platform.isIOS) {
            _speechTimer = Timer.periodic(const Duration(seconds: 5), (_) {
              if (status == "listening" && recognizedWord.isNotEmpty) {
                if (recognizedWord.isEmpty) {
                  recognizedWord = '';
                }
                _processLatestText(recognizedWord);
              }
            });
          } else {
            if (status == "done") {
              // add(RestartSpeechRecognisation(words: recognizedWord));
              await startListing(event.speechToText, emit);
              recognizedWord = '';
            }
          }
        },
      );

      if (initialized) {
        await startListing(event.speechToText, emit);
      } else {
        // emit(const StartSpeechToText(isTextToSpeechStart: false));
        // emit(const UnityScreenError(message: "Permission Denied"));
      }
    } else {
      final status = await _reqAudioPermission();
      if (status == PermissionStatus.granted) {
        _startListening();
      }
    }
  }

  FutureOr<void> _onStopSpeechRecognition(
    StopSpeechRecognition event,
    Emitter<SpeechTranscriptState> emit,
  ) {
    if (Platform.isIOS) {
      event.speechToText.stop();
      _speechTimer?.cancel;
      recognizedWord = '';
      lastResponse = "";
    } else {
      _aslFlutterInputPlugin.stopListening();
      _androidSpeechTimer?.cancel();
      recognizedWord = '';
      lastResponse = "";
    }
    // emit(const StartSpeechToText(isTextToSpeechStart: false));
    speechTexts.clear();
    speechWords.clear();
    speechTextIndex = 0;
    isAnimating = false;
    noResponseCount = 0;
    lastUpdatedText = "";
  }

  FutureOr<void> _onRestartSpeechRecognition(
    RestartSpeechRecognition event,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    if (event.words.isNotEmpty && !emit.isDone) {
      try {
        final texts =
            await textConversionService.getTranscribedText(event.words);
        if (texts.isNotEmpty && !emit.isDone) {
          for (var textItem in texts) {
            final rootWord = textItem.root;
            final speechWord = textItem.word;
            speechTexts.add(rootWord);
            speechWords.add(speechWord);
            if (!isAnimating) {
              for (var item in speechTexts[speechTextIndex]) {
                rootWords.add(item);
                print("startSpeechToTextProcessing =>rootWords $rootWords");
                if (item.contains(',')) {
                  final splitWords = item.split(',');

                  for (int i = 0; i < splitWords.length; i++) {
                    var word = splitWords[i];
                    print("speechTextItem =>$word");
                    await Future.delayed(const Duration(milliseconds: 150), () {
                      emit(SendMessagesToSpeechScreen(
                        message: {
                          'root': word,
                          'word': textItem.word.toString()
                        },
                        currentAnimation: state.currentAnimation,
                        // isTextToSpeechStart: state.isTextToSpeechStart,
                        // isVideoButtonDisabled: state.isVideoButtonDisabled,
                        // isRecorderButtonDisabled: state.isRecorderButtonDisabled,
                        // isDictionaryButtonDisabled:
                        //     state.isDictionaryButtonDisabled,
                        // isTextFormFieldDisabled: state.isTextFormFieldDisabled,
                      ));
                    });
                  }
                } else {
                  emit(SendMessagesToSpeechScreen(
                    message: {'root': item, 'word': textItem.word.toString()},
                    currentAnimation: state.currentAnimation,
                    // isTextToSpeechStart: state.isTextToSpeechStart,
                    // isVideoButtonDisabled: state.isVideoButtonDisabled,
                    // isRecorderButtonDisabled: state.isRecorderButtonDisabled,
                    // isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
                    // isTextFormFieldDisabled: state.isTextFormFieldDisabled,
                  ));
                }
              }
            }
          }
        } else {
          if (Platform.isIOS) {
            _speechTimer?.cancel();
          } else {
            _androidSpeechTimer?.cancel();
          }
          emit(const SpeechScreenError(message: "Failed to process speech"));
        }
      } catch (e) {
        print("SpeechTranscriptBloc: Error in _onRestartSpeechRecognition: $e");
        if (!emit.isDone) {
          emit(const SpeechScreenError(message: "Failed to process speech"));
        }
      }
    }
  }

  Future<void> _startListening() async {
    try {
      await _setAPIKey();
      // await _aslFlutterInputPlugin.setCurrentLanguage('ar-SA');
      await _aslFlutterInputPlugin.initAudioTranscription();
      await _startTimer();
    } on Exception catch (e) {
      debugPrint('[Exception][_startListening]: $e');
    }
  }

  // Android permission request method - same as Unity
  Future<PermissionStatus> _reqAudioPermission() async {
    return await Permission.microphone.request();
  }

  // void _processRecognizedText(String text) {
  //   if (text.isNotEmpty && text != lastResponse) {
  //     lastResponse = text;
  //     add(RestartSpeechRecognition(words: text));
  //   }
  // }

  void _processLatestText(String newText) {
    if (newText.isNotEmpty && newText != lastProcessedText) {
      String latestPart = '';

      // Find the index where the new text differs from the old text
      int diffIndex = 0;
      while (diffIndex < newText.length &&
          diffIndex < lastProcessedText.length &&
          newText[diffIndex] == lastProcessedText[diffIndex]) {
        diffIndex++;
      }

      // Extract only the new part of the text
      latestPart = newText.substring(diffIndex);

      print('Current Response => $newText');
      print('New text to process: $latestPart');

      // Process the latest part of the text
      _handleLatestText(latestPart);

      // Update the last processed text
      lastProcessedText = newText;
      add(RestartSpeechRecognition(words: latestPart));
    }
  }

  void _handleLatestText(String text) {
    // Implement your processing logic here
    print('Processing latest part: $text');
  }

  Future<void> startListing(
    SpeechToText speech,
    Emitter<SpeechTranscriptState> emit,
  ) async {
    await speech.listen(
      onResult: (res) {
        print("res=> ${res.recognizedWords}");
        recognizedWord = res.recognizedWords;
      },
      localeId: "ar-SA",
    );
  }

  Future<void> _setAPIKey() async {
    try {
      final api = await _aslFlutterInputPlugin
          .setAPIKey("AIzaSyCizaPQJGhorO8m00L3uBuJzX3H5hVm_2c");
      print("_setAPIKey => $api");
    } catch (e) {
      print("Error =>  _setAPIKey => $e");
    }
  }

  _startTimer() async {
    _androidSpeechTimer = Timer.periodic(const Duration(seconds: 5), (_) async {
      recognizedWord = await _aslFlutterInputPlugin.fetchTranscribedText();
      print("Current Response =>recognizedWord $recognizedWord ");
      if (recognizedWord.contains('؟')) {
        recognizedWord = recognizedWord.replaceAll('؟', '');
      }
      if (recognizedWord.isEmpty) {
        print("No transcription received.");
        recognizedWord = ''; // Skip processing if no text was recognized
      } else {
        // print("New text to process: ${_processLatestText(recognizedWord)}");
        if (lastUpdatedText != recognizedWord) {
          lastUpdatedText = recognizedWord;
          noResponseCount = 0;
        } else {
          noResponseCount = noResponseCount + 1;
          if (noResponseCount >= 10) {
            isAnimating = false;
          }
        }
        _processLatestText(recognizedWord);
      }
    });
  }

  FutureOr<void> _onUploadFileForTranscription(UploadFileForTranscription event,
      Emitter<SpeechTranscriptState> emit) async {
    try {
      // Reset cancellation flag for new process
      _shouldStopAnimationProcesses = false;

      // Emit loading state to show progress indicator
      emit(const FileUploadLoadingState());

      // Call the recording service to get transcription from uploaded file
      final recorderList = await recordingService.getTranscriptionFromRecording(
        event.fileBytes,
        fileName: event.fileName,
      );

      if (recorderList.isEmpty) {
        emit(const SpeechScreenError(
            message: "No transcription found. Please try again."));
        return;
      }

      // Process the transcription results similar to TranslateButtonTapped
      emit(const SpeechTranscriptInitial(
        isRecordingEnabled: false,
        isTranslateButtonDisabled: true,
        isDataLoading: false,
      ));

      // Send messages to Unity for each transcription result
      for (var item in recorderList) {
        // Check if we should stop the animation processes
        if (_shouldStopAnimationProcesses) {
          break;
        }

        final rootWord = item.root ?? "";
        if (rootWord.contains(',')) {
          final splitWords = rootWord.split(',');
          for (var word in splitWords) {
            // Check again before emitting
            if (_shouldStopAnimationProcesses) {
              break;
            }
            emit(SendMessagesToSpeechScreen(
                message: {'root': word, 'word': item.word},
                currentAnimation: state.currentAnimation));
            // await Future.delayed(const Duration(milliseconds: 150));
          }
        } else {
          emit(SendMessagesToSpeechScreen(
              message: {'root': item.root, 'word': item.word},
              currentAnimation: state.currentAnimation));
          // await Future.delayed(const Duration(milliseconds: 150));
        }
      }
    } catch (e) {
      print('Error uploading file for transcription: $e');
      emit(const SpeechScreenError(
          message: "Failed to process uploaded file. Please try again."));
    }
  }

  FutureOr<void> _onStopAllAnimationProcesses(StopAllAnimationProcesses event,
      Emitter<SpeechTranscriptState> emit) async {
    // Set the cancellation flag
    _shouldStopAnimationProcesses = true;

    // Cancel any running timers
    _speechTimer?.cancel();
    _androidSpeechTimer?.cancel();

    // Reset animation state
    isAnimating = false;

    // Clear any pending animation text
    emit(const SpeechTranscriptInitial());
  }
}
