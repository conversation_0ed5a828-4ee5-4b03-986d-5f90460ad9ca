part of 'speech_transcript_bloc.dart';

@immutable
sealed class SpeechTranscriptEvent {}

class StartRecording extends SpeechTranscriptEvent {
  final bool isRecordingEnabled;
  final RecorderController controller;
  final String path;

  StartRecording(
      {required this.isRecordingEnabled,
      required this.controller,
      required this.path});
}

class StopRecording extends SpeechTranscriptEvent {
  final bool isRecordingEnabled;
  final RecorderController controller;
  final String path;

  StopRecording(
      {required this.isRecordingEnabled,
      required this.controller,
      required this.path});
}

class ResetSpeechTranscription extends SpeechTranscriptEvent {
  final RecorderController controller;

  ResetSpeechTranscription({required this.controller});
}

class TranslateButtonTapped extends SpeechTranscriptEvent {
  final RecorderController controller;
  final String path;

  TranslateButtonTapped({required this.controller, required this.path});
}

class UpdateCurrentAnimationText extends SpeechTranscriptEvent {
  final String animationText;

  UpdateCurrentAnimationText({required this.animationText});
}

class StartSpeechRecognition extends SpeechTranscriptEvent {
  final SpeechToText speechToText;

  StartSpeechRecognition({required this.speechToText});
}

class StopSpeechRecognition extends SpeechTranscriptEvent {
  final SpeechToText speechToText;

  StopSpeechRecognition({required this.speechToText});
}

class RestartSpeechRecognition extends SpeechTranscriptEvent {
  final String words;

  RestartSpeechRecognition({required this.words});
}

class UploadFileForTranscription extends SpeechTranscriptEvent {
  final Uint8List fileBytes;
  final String fileName;

  UploadFileForTranscription({
    required this.fileBytes,
    required this.fileName,
  });
}

class StopAllAnimationProcesses extends SpeechTranscriptEvent {
  StopAllAnimationProcesses();
}
