import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../data/service/auth_service.dart';

// Events
abstract class SignupEvent extends Equatable {
  const SignupEvent();

  @override
  List<Object> get props => [];
}

class SignupRequested extends SignupEvent {
  final String name;
  final String email;
  final String password;
  final String mobile;
  final String nationality;
  final String verificationCode;

  const SignupRequested({
    required this.name,
    required this.email,
    required this.password,
    required this.mobile,
    required this.nationality,
    required this.verificationCode,
  });

  @override
  List<Object> get props =>
      [name, email, password, mobile, nationality, verificationCode];
}

// States
abstract class SignupState extends Equatable {
  const SignupState();

  @override
  List<Object?> get props => [];
}

class SignupInitial extends SignupState {}

class SignupLoading extends SignupState {}

class SignupSuccess extends SignupState {
  final String message;

  const SignupSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class SignupError extends SignupState {
  final String message;

  const SignupError({required this.message});

  @override
  List<Object?> get props => [message];
}

// SignupBloc
class SignupBloc extends Bloc<SignupEvent, SignupState> {
  final AuthService _authService = AuthService();

  SignupBloc() : super(SignupInitial()) {
    on<SignupRequested>(_onSignupRequested);
  }

  Future<void> _onSignupRequested(
    SignupRequested event,
    Emitter<SignupState> emit,
  ) async {
    emit(SignupLoading());

    final response = await _authService.signup(
      event.name,
      event.email,
      event.password,
      event.mobile,
      event.nationality,
      event.verificationCode,
    );

    if (response.isSuccess) {
      emit(SignupSuccess(
          message: response.message ??
              'Registration successful. You can now log in.'));
    } else {
      emit(SignupError(message: response.errorMessage));
    }
  }
}
