import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/service/auth_service.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class LoginRequested extends AuthEvent {
  final String email;
  final String password;

  const LoginRequested({required this.email, required this.password});

  @override
  List<Object> get props => [email, password];
}

class LogoutRequested extends AuthEvent {}

class VerifyEmailRequested extends AuthEvent {
  final String email;

  const VerifyEmailRequested({required this.email});

  @override
  List<Object> get props => [email];
}

class GoogleSignInRequested extends AuthEvent {
  final String idToken;

  const GoogleSignInRequested({required this.idToken});

  @override
  List<Object> get props => [idToken];
}

class CompleteGoogleProfileRequested extends AuthEvent {
  final String tempUserId;
  final String mobile;
  final String nationality;

  const CompleteGoogleProfileRequested({
    required this.tempUserId,
    required this.mobile,
    required this.nationality,
  });

  @override
  List<Object> get props => [tempUserId, mobile, nationality];
}

// States
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class EmailVerificationLoading extends AuthState {}

class EmailVerificationSuccess extends AuthState {
  final String message;
  final String email;

  const EmailVerificationSuccess({required this.message, required this.email});

  @override
  List<Object> get props => [message, email];
}

class EmailVerificationError extends AuthState {
  final String message;

  const EmailVerificationError({required this.message});

  @override
  List<Object> get props => [message];
}

class GoogleSignInLoading extends AuthState {}

class GoogleSignInNeedsCompletion extends AuthState {
  final String tempUserId;

  const GoogleSignInNeedsCompletion({required this.tempUserId});

  @override
  List<Object> get props => [tempUserId];
}

class GoogleProfileCompletionLoading extends AuthState {}

class GoogleProfileCompletionError extends AuthState {
  final String message;

  const GoogleProfileCompletionError({required this.message});

  @override
  List<Object> get props => [message];
}

// Update the AuthAuthenticated state
class AuthAuthenticated extends AuthState {
  final String accessToken;
  final String refreshToken;
  final int roleId;
  final String tokenType;

  const AuthAuthenticated({
    required this.accessToken,
    required this.refreshToken,
    required this.roleId,
    required this.tokenType,
  });

  @override
  List<Object?> get props => [accessToken, refreshToken, roleId, tokenType];
}

class AuthError extends AuthState {
  final String message;

  const AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

// AuthBloc update
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService = AuthService();

  AuthBloc() : super(AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<LogoutRequested>(_onLogoutRequested);
    on<VerifyEmailRequested>(_onVerifyEmailRequested);
    on<GoogleSignInRequested>(_onGoogleSignInRequested);
    on<CompleteGoogleProfileRequested>(_onCompleteGoogleProfileRequested);
  }

  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    final response = await _authService.login(event.email, event.password);

    if (response.isSuccess) {
      // Save tokens to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(kEYAccessToken, response.accessToken!);
      await prefs.setString(kEYRefreshToken, response.refreshToken!);
      await prefs.setInt(kEYRoleId, response.roleId!);
      await prefs.setString(kEYTokenType, response.tokenType!);

      emit(AuthAuthenticated(
        accessToken: response.accessToken!,
        refreshToken: response.refreshToken!,
        roleId: response.roleId!,
        tokenType: response.tokenType!,
      ));
    } else {
      emit(AuthError(message: response.errorMessage));
    }
  }

  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    // Clear tokens from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    await prefs.remove('refresh_token');
    await prefs.remove('role_id');

    emit(AuthInitial());
  }

  Future<void> _onVerifyEmailRequested(
    VerifyEmailRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(EmailVerificationLoading());

    final response = await _authService.verifyEmail(event.email);

    if (response.isSuccess) {
      emit(EmailVerificationSuccess(
        message: response.message ?? 'Verification code sent to your email.',
        email: event.email,
      ));
    } else {
      // Check if the error is about email already existing
      String errorMessage = response.errorMessage;
      if (response.message?.toLowerCase().contains('email already exists') == true ||
          response.detail?.toLowerCase().contains('email already exists') == true) {
        errorMessage = 'User already exists';
      }

      emit(EmailVerificationError(message: errorMessage));
    }
  }

  Future<void> _onGoogleSignInRequested(
    GoogleSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(GoogleSignInLoading());

    final response = await _authService.googleSignIn(event.idToken);

    if (response.isSuccess) {
      if (response.needsCompletion == true) {
        // New user needs to complete profile
        emit(GoogleSignInNeedsCompletion(tempUserId: response.tempUserId!));
      } else {
        // Existing user, login successful
        emit(AuthAuthenticated(
          accessToken: response.accessToken!,
          refreshToken: response.refreshToken!,
          roleId: response.roleId!,
          tokenType: response.tokenType!,
        ));
      }
    } else {
      emit(AuthError(message: response.errorMessage));
    }
  }

  Future<void> _onCompleteGoogleProfileRequested(
    CompleteGoogleProfileRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(GoogleProfileCompletionLoading());

    final response = await _authService.completeGoogleProfile(
      event.tempUserId,
      event.mobile,
      event.nationality,
    );

    if (response.isSuccess) {
      emit(AuthAuthenticated(
        accessToken: response.accessToken!,
        refreshToken: response.refreshToken!,
        roleId: response.roleId!,
        tokenType: response.tokenType!,
      ));
    } else {
      emit(GoogleProfileCompletionError(message: response.errorMessage));
    }
  }
}
