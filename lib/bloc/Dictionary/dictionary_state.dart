part of 'dictionary_bloc.dart';

abstract class DictionaryState extends Equatable {
  final int selectedTabIndex;
  final String searchQuery;
  final String selectedItem;
  final String currentAnimation;

  const DictionaryState({
    this.selectedTabIndex = 0,
    this.searchQuery = '',
    this.selectedItem = '',
    this.currentAnimation = '',
  });

  @override
  List<Object> get props =>
      [selectedTabIndex, searchQuery, selectedItem, currentAnimation];
}

class DictionaryInitial extends DictionaryState {
  const DictionaryInitial({
    super.selectedTabIndex,
    super.searchQuery,
    super.selectedItem,
    super.currentAnimation,
  });
}

class DictionaryTabChanged extends DictionaryState {
  const DictionaryTabChanged({
    required super.selectedTabIndex,
    super.searchQuery,
    super.selectedItem,
    super.currentAnimation,
  });

  @override
  List<Object> get props => [
        selectedTabIndex,
        searchQuery,
        selectedItem,
        currentAnimation,
        'tab_changed'
      ];
}

class DictionarySearchUpdated extends DictionaryState {
  const DictionarySearchUpdated({
    super.selectedTabIndex,
    required super.searchQuery,
    super.selectedItem,
    super.currentAnimation,
  });

  @override
  List<Object> get props => [
        selectedTabIndex,
        searchQuery,
        selectedItem,
        currentAnimation,
        'search_updated'
      ];
}

class DictionaryItemSelected extends DictionaryState {
  final Map<String, dynamic> message;
  const DictionaryItemSelected({
    required this.message,
    super.selectedTabIndex,
    super.searchQuery,
    required super.selectedItem,
    super.currentAnimation,
  });

  @override
  List<Object> get props =>
      [selectedTabIndex, searchQuery, selectedItem, currentAnimation, message];
}

/// State for updating current animation text
class UpdateCurrentAnimation extends DictionaryState {
  const UpdateCurrentAnimation({
    super.selectedTabIndex,
    super.searchQuery,
    super.selectedItem,
    super.currentAnimation,
  });

  @override
  List<Object> get props => [
        selectedTabIndex,
        searchQuery,
        selectedItem,
        currentAnimation,
        'animation_updated'
      ];
}
