part of 'dictionary_bloc.dart';

abstract class DictionaryEvent extends Equatable {
  const DictionaryEvent();

  @override
  List<Object> get props => [];
}

class ChangeTab extends DictionaryEvent {
  final int tabIndex;

  const ChangeTab({required this.tabIndex});

  @override
  List<Object> get props => [tabIndex];
}

class SearchDictionary extends DictionaryEvent {
  final String query;

  const SearchDictionary({required this.query});

  @override
  List<Object> get props => [query];
}

class SelectDictionaryItem extends DictionaryEvent {
  final String item;

  const SelectDictionaryItem({required this.item});

  @override
  List<Object> get props => [item];
}

class UpdateCurrentAnimationText extends DictionaryEvent {
  final String animationText;

  const UpdateCurrentAnimationText({required this.animationText});

  @override
  List<Object> get props => [animationText];
}
