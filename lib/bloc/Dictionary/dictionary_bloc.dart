import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'dictionary_event.dart';
part 'dictionary_state.dart';

class DictionaryBloc extends Bloc<DictionaryEvent, DictionaryState> {
  List<String> rootWords = [];
  List<List<String>> speechTexts = [];
  int speechTextIndex = 0;
  DictionaryBloc() : super(const DictionaryInitial()) {
    on<ChangeTab>(_onChangeTab);
    on<SearchDictionary>(_onSearchDictionary);
    on<SelectDictionaryItem>(_onSelectDictionaryItem);
    on<UpdateCurrentAnimationText>(_onUpdateCurrentAnimationText);
  }

  FutureOr<void> _onChangeTab(ChangeTab event, Emitter<DictionaryState> emit) {
    emit(DictionaryTabChanged(
      selectedTabIndex: event.tabIndex,
      searchQuery: state.searchQuery,
      selectedItem: state.selectedItem,
      currentAnimation: state.currentAnimation,
    ));
  }

  FutureOr<void> _onSearchDictionary(
      SearchDictionary event, Emitter<DictionaryState> emit) {
    emit(DictionarySearchUpdated(
      selectedTabIndex: state.selectedTabIndex,
      searchQuery: event.query,
      selectedItem: state.selectedItem,
      currentAnimation: state.currentAnimation,
    ));
  }

  FutureOr<void> _onSelectDictionaryItem(
      SelectDictionaryItem event, Emitter<DictionaryState> emit) {
    emit(DictionaryItemSelected(
      message: {'root': event.item, 'word': event.item},
      selectedTabIndex: state.selectedTabIndex,
      searchQuery: state.searchQuery,
      selectedItem: event.item,
      currentAnimation: state.currentAnimation,
    ));
  }

  FutureOr<void> _onUpdateCurrentAnimationText(
      UpdateCurrentAnimationText event, Emitter<DictionaryState> emit) {
    emit(UpdateCurrentAnimation(
      selectedTabIndex: state.selectedTabIndex,
      searchQuery: state.searchQuery,
      selectedItem: state.selectedItem,
      currentAnimation: event.animationText,
    ));
  }
}
