import 'package:arabic_sign_language/bloc/Dictionary/dictionary_bloc.dart';
import 'package:arabic_sign_language/bloc/UnityScreen/unity_screen_bloc.dart';
import 'package:arabic_sign_language/bloc/VideoTranscription/video_transcription_bloc.dart';
import 'package:arabic_sign_language/bloc/auth/auth_bloc.dart';
import 'package:arabic_sign_language/bloc/connectionChecker/connection_checker_bloc.dart';
import 'package:arabic_sign_language/bloc/language/language_bloc.dart';
import 'package:arabic_sign_language/bloc/speechTranscript/speech_transcript_bloc.dart';
import 'package:arabic_sign_language/bloc/textTranscript/text_transcript_bloc.dart';
import 'package:arabic_sign_language/bloc/youtubeScreen/youtube_screen_bloc.dart';
import 'package:arabic_sign_language/data/service/auth_service.dart';
import 'package:arabic_sign_language/data/service/recoding_service.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:arabic_sign_language/data/service/transcript_service.dart';
import 'package:arabic_sign_language/firebase_options.dart';
import 'package:arabic_sign_language/presentation/core/themes/theme.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'presentation/screens/splash/splash_screen.dart';
import 'package:flutter/material.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  runApp(EasyLocalization(
    supportedLocales: const [Locale('en'), Locale('ar')],
    path: 'assets/lang',
    fallbackLocale: const Locale('en'),
    child: MultiBlocProvider(
      providers: [
        BlocProvider<UnityScreenBloc>(
          create: (context) => UnityScreenBloc(
            RecordingService(),
            TextConversionService(),
            TranscriptService(),
          ),
        ),
        BlocProvider<YoutubeScreenBloc>(
          create: (context) => YoutubeScreenBloc(
            TranscriptService(),
          ),
        ),
        BlocProvider(
          create: (context) =>
              ConnectionCheckerBloc()..add(ConnectivityObserver()),
        ),
        BlocProvider<LanguageBloc>(create: (context) => LanguageBloc()),
        // Add this to your existing BlocProvider list
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(),
        ),
        BlocProvider<TextTranscriptBloc>(
          create: (context) => TextTranscriptBloc(
            TextConversionService(),
          ),
        ),
        BlocProvider<SpeechTranscriptBloc>(
          create: (context) => SpeechTranscriptBloc(
            recordingService: RecordingService(),
            textConversionService: TextConversionService(),
          ),
        ),
        BlocProvider<VideoTranscriptionBloc>(
          create: (context) => VideoTranscriptionBloc(
            transcriptService: TranscriptService(),
          ),
        ),
        BlocProvider<DictionaryBloc>(
          create: (context) => DictionaryBloc(),
        )
      ],
      child: const MyApp(),
    ),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: AuthService.navigatorKey,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      debugShowCheckedModeBanner: false,
      title: 'ASL',
      themeMode: ThemeMode.dark,
      theme: themeLight(context),
      darkTheme: themeDark(context),
      home: const SplashScreen(),
      builder: (context, child) {
        final size = MediaQuery.sizeOf(context);
        return BlocListener<ConnectionCheckerBloc, ConnectionCheckerState>(
          listener: (context, state) {
            if (state is ConnectivityStatusChanged) {
              final isConnected = state.isNetworkConnected;
              final snackBar = SnackBar(
                content: Text(
                  isConnected
                      ? "Connected to the internet"
                      : "No internet connection",
                ),
                backgroundColor: isConnected ? Colors.green : Colors.red,
                behavior: SnackBarBehavior.floating,
                duration: Duration(
                  seconds: isConnected ? 3 : 1800,
                ),
                margin: EdgeInsets.only(
                  bottom: size.height * 0.05,
                  right: 10,
                  left: 10,
                ),
              );

              // Show the snackbar using ScaffoldMessenger
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              ScaffoldMessenger.of(context).showSnackBar(snackBar);
            }
          },
          child: child,
        );
      },
    );
  }
}
