import 'package:arabic_sign_language/data/models/recording_model/recording_model.dart';
import 'package:arabic_sign_language/data/service/base_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../presentation/core/url.dart';

class RecordingService extends BaseService {
  RecordingService()
      : super(
          baseUrl: AUTH_BASE_URL,
          headers: {"Content-Type": "multipart/form-data"},
        );

  Future<List<RecordingModel>> getTranscriptionFromRecording(
      Uint8List recording,
      {String? fileName}) async {
    List<RecordingModel> recordingData = [];
    try {
      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(recording,
            filename: fileName ?? 'recording.m4a')
      });

      // Increase timeout for file upload transcription (2 minutes)
      final response = await dio.post(
        uploadVoice,
        data: formData,
        options: Options(
          receiveTimeout:
              const Duration(minutes: 2), // 2 minutes for transcription
          sendTimeout: const Duration(minutes: 1), // 1 minute for upload
        ),
      );

      print("response => getTranscriptionFromRecording => ${response.data}");
      if (response.statusCode == 200 && response.data['status']) {
        final List<dynamic> data = response.data['data'];
        print("recordings => getTranscriptionFromRecording => $data");
        recordingData = data.map((e) => RecordingModel.fromJson(e)).toList();
        print("recordings => getTranscriptionFromRecording => $recordingData");
      }
      return recordingData;
    } catch (e) {
      print("error => getTranscriptionFromRecording =>$e");
      return recordingData;
    }
  }
}
