class ResetPasswordResponse {
  final bool status;
  final String? message;
  final String? detail;

  ResetPasswordResponse({
    this.status = false,
    this.message,
    this.detail,
  });

  factory ResetPasswordResponse.fromJson(Map<String, dynamic> json) {
    return ResetPasswordResponse(
      status: json['message'] != null,
      message: json['message'],
      detail: json['detail'],
    );
  }

  String get errorMessage => detail ?? 'An error occurred. Please try again.';
  bool get isSuccess => status;
}
