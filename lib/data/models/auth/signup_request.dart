import 'package:json_annotation/json_annotation.dart';

part 'signup_request.g.dart';

@JsonSerializable()
class SignupRequest {
  final String name;
  final String mobile;
  final String email;
  final String password;
  final String nationality;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'verification_code')
  final String verificationCode;

  SignupRequest({
    required this.name,
    required this.mobile,
    required this.email,
    required this.password,
    required this.nationality,
    required this.verificationCode,
  });

  Map<String, dynamic> toJson() => _$SignupRequestToJson(this);
}
