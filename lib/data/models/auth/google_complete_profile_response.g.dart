// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'google_complete_profile_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GoogleCompleteProfileResponse _$GoogleCompleteProfileResponseFromJson(
        Map<String, dynamic> json) =>
    GoogleCompleteProfileResponse(
      status: json['status'] as bool,
      accessToken: json['access_token'] as String?,
      refreshToken: json['refresh_token'] as String?,
      tokenType: json['token_type'] as String?,
      roleId: (json['role_id'] as num?)?.toInt(),
      detail: json['detail'] as String?,
      message: json['message'] as String?,
      user: json['user'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$GoogleCompleteProfileResponseToJson(
        GoogleCompleteProfileResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'access_token': instance.accessToken,
      'refresh_token': instance.refreshToken,
      'token_type': instance.tokenType,
      'role_id': instance.roleId,
      'detail': instance.detail,
      'message': instance.message,
      'user': instance.user,
    };
