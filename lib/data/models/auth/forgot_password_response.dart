class ForgotPasswordResponse {
  final bool status;
  final String? message;
  final String? detail;

  ForgotPasswordResponse({
    this.status = false,
    this.message,
    this.detail,
  });

  factory ForgotPasswordResponse.fromJson(Map<String, dynamic> json) {
    return ForgotPasswordResponse(
      status: json['message'] != null,
      message: json['message'],
      detail: json['detail'],
    );
  }

  String get errorMessage => detail ?? 'An error occurred. Please try again.';
  bool get isSuccess => status;
}