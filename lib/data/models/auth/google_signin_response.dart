import 'package:json_annotation/json_annotation.dart';

part 'google_signin_response.g.dart';

@JsonSerializable()
class GoogleSigninResponse {
  final bool status;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'needs_completion')
  final bool? needsCompletion;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'temp_user_id')
  final String? tempUserId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'access_token')
  final String? accessToken;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_token')
  final String? refreshToken;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'token_type')
  final String? tokenType;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'role_id')
  final int? roleId;
  
  final String? detail;
  final String? message;
  
  final Map<String, dynamic>? user;

  GoogleSigninResponse({
    required this.status,
    this.needsCompletion,
    this.tempUserId,
    this.accessToken,
    this.refreshToken,
    this.tokenType,
    this.roleId,
    this.detail,
    this.message,
    this.user,
  });

  factory GoogleSigninResponse.from<PERSON>son(Map<String, dynamic> json) =>
      _$GoogleSigninResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GoogleSigninResponseToJson(this);
  
  bool get isSuccess => status;
  String get errorMessage => detail ?? message ?? 'Unknown error occurred';
}
