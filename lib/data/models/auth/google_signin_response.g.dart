// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'google_signin_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GoogleSigninResponse _$GoogleSigninResponseFromJson(
        Map<String, dynamic> json) =>
    GoogleSigninResponse(
      status: json['status'] as bool,
      needsCompletion: json['needs_completion'] as bool?,
      tempUserId: json['temp_user_id'] as String?,
      accessToken: json['access_token'] as String?,
      refreshToken: json['refresh_token'] as String?,
      tokenType: json['token_type'] as String?,
      roleId: (json['role_id'] as num?)?.toInt(),
      detail: json['detail'] as String?,
      message: json['message'] as String?,
      user: json['user'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$GoogleSigninResponseToJson(
        GoogleSigninResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'needs_completion': instance.needsCompletion,
      'temp_user_id': instance.tempUserId,
      'access_token': instance.accessToken,
      'refresh_token': instance.refreshToken,
      'token_type': instance.tokenType,
      'role_id': instance.roleId,
      'detail': instance.detail,
      'message': instance.message,
      'user': instance.user,
    };
