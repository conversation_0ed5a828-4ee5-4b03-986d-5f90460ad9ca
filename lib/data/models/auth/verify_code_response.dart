import 'package:json_annotation/json_annotation.dart';

part 'verify_code_response.g.dart';

@JsonSerializable()
class VerifyCodeResponse {
  final String? message;
  final String? detail;

  VerifyCodeResponse({
    this.message,
    this.detail,
  });

  factory VerifyCodeResponse.fromJson(Map<String, dynamic> json) => 
      _$VerifyCodeResponseFromJson(json);
  
  String get errorMessage => detail ?? "Verification failed";
  bool get isSuccess => message != null && detail == null;
}