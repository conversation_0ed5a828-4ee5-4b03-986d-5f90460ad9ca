import 'package:json_annotation/json_annotation.dart';

part 'email_verification_response.g.dart';

@JsonSerializable()
class EmailVerificationResponse {
  final bool status;
  final String? message;
  final dynamic detail;

  EmailVerificationResponse({
    this.status = true,
    this.message,
    this.detail,
  });

  factory EmailVerificationResponse.fromJson(Map<String, dynamic> json) =>
      _$EmailVerificationResponseFromJson(json);

  String get errorMessage {
    if (detail is List) {
      // Handle FastAPI validation error format
      try {
        final errors = detail as List;
        if (errors.isNotEmpty && errors[0] is Map) {
          final firstError = errors[0] as Map;
          return firstError['msg'] ?? "Email verification failed";
        }
      } catch (e) {
        // Fallback if parsing fails
      }
    } else if (detail is String) {
      return detail;
    }
    return message ?? "Email verification failed";
  }

  bool get isSuccess => status && detail == null;
}
