import 'package:json_annotation/json_annotation.dart';

part 'google_complete_profile_request.g.dart';

@JsonSerializable()
class GoogleCompleteProfileRequest {
  @JsonKey(name: 'temp_user_id')
  final String tempUserId;
  
  final String mobile;
  final String nationality;

  GoogleCompleteProfileRequest({
    required this.tempUserId,
    required this.mobile,
    required this.nationality,
  });

  Map<String, dynamic> toJson() => _$GoogleCompleteProfileRequestToJson(this);
}
