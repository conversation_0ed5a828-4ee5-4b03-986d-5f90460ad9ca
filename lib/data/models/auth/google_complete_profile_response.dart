import 'package:json_annotation/json_annotation.dart';

part 'google_complete_profile_response.g.dart';

@JsonSerializable()
class GoogleCompleteProfileResponse {
  final bool status;
  
  @J<PERSON><PERSON><PERSON>(name: 'access_token')
  final String? accessToken;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_token')
  final String? refreshToken;
  
  @J<PERSON><PERSON><PERSON>(name: 'token_type')
  final String? tokenType;
  
  @Json<PERSON>ey(name: 'role_id')
  final int? roleId;
  
  final String? detail;
  final String? message;
  
  final Map<String, dynamic>? user;

  GoogleCompleteProfileResponse({
    required this.status,
    this.accessToken,
    this.refreshToken,
    this.tokenType,
    this.roleId,
    this.detail,
    this.message,
    this.user,
  });

  factory GoogleCompleteProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$GoogleCompleteProfileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GoogleCompleteProfileResponseTo<PERSON>son(this);
  
  bool get isSuccess => status;
  String get errorMessage => detail ?? message ?? 'Unknown error occurred';
}
