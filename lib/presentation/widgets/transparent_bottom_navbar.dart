import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';

class TransparentBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final VoidCallback? onMoreTap;
  final VoidCallback? onLongPress;
  final Function(int)? onItemLongPress;

  const TransparentBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    this.onMoreTap,
    this.onLongPress,
    this.onItemLongPress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: currentNavItems,
      builder: (context, navItems, _) {
        return ValueListenableBuilder(
          valueListenable: isCustomizationMode,
          builder: (context, customMode, _) {
            return Container(
              height: 60,
              width: double.infinity,
              color: Colors.transparent,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Dynamic navigation items
                  ...List.generate(
                    navItems.length > 4 ? 4 : navItems.length,
                    (index) => _buildNavItem(
                      index,
                      navItems[index].image,
                      navItems[index].title,
                      customMode,
                    ),
                  ),
                  // More button (always last)
                  _buildNavItem(
                    navItems.length > 4 ? 4 : navItems.length,
                    IC_MORE,
                    'more',
                    customMode,
                    isMoreButton: true,
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildNavItem(int index, String image, String label, bool customMode,
      {bool isMoreButton = false}) {
    final bool isSelected = index == currentIndex;

    return GestureDetector(
      onTap: () {
        if (isMoreButton && onMoreTap != null) {
          // Handle More button specially
          onMoreTap!();
        } else {
          onTap(index);
        }
      },
      onLongPress: customMode && !isMoreButton
          ? () {
              // Handle long press for removing items in customization mode
              if (onItemLongPress != null) {
                onItemLongPress!(index);
              }
            }
          : (!isMoreButton && onLongPress != null)
              ? () {
                  // Handle long press for starting customization
                  onLongPress!();
                }
              : null,
      child: Container(
        decoration: customMode && !isMoreButton
            ? BoxDecoration(
                border: Border.all(
                  color: Colors.white.withOpacity(0.5),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              )
            : null,
        padding: customMode && !isMoreButton ? const EdgeInsets.all(4) : null,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                  color: isSelected
                      ? const Color.fromARGB(52, 132, 75, 237)
                      : Colors.transparent,
                  borderRadius: const BorderRadius.all(Radius.circular(5))),
              child: Stack(
                children: [
                  Image.asset(
                    image,
                    height: 24,
                    width: 24,
                  ),
                  if (customMode && !isMoreButton)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 8,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label.tr(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontFamily: FONT_FAMILY,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
