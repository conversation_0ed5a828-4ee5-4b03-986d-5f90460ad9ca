import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:country_picker/country_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../bloc/auth/auth_bloc.dart';
import '../../core/constants.dart';
import '../../widgets/custom_gradient_button.dart';
import '../home/<USER>';

class CompleteGoogleProfileScreen extends StatefulWidget {
  final String tempUserId;

  const CompleteGoogleProfileScreen({
    Key? key,
    required this.tempUserId,
  }) : super(key: key);

  @override
  State<CompleteGoogleProfileScreen> createState() => _CompleteGoogleProfileScreenState();
}

class _CompleteGoogleProfileScreenState extends State<CompleteGoogleProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Focus nodes
  final _phoneFocusNode = FocusNode();

  String countryName = '';
  String countryCode = 'SA';
  String countryDialCode = '+966';

  // Country validation error (not handled by form validation)
  String? _countryError;

  @override
  void dispose() {
    _phoneController.dispose();
    _phoneFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Method to clear focus and hide keyboard
  void _clearFocus() {
    _phoneFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent going back
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: _buildTransparentAppBar(),
        resizeToAvoidBottomInset: false,
        body: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is GoogleProfileCompletionLoading) {
              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            } else if (state is AuthAuthenticated) {
              // Close loading dialog if open
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }

              // Navigate to home screen
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (_) => const HomeScreen()),
                (route) => false,
              );
            } else if (state is GoogleProfileCompletionError) {
              // Close loading dialog if open
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }

              _clearFocus();

              // Use a slight delay to ensure focus is cleared before showing the snackbar
              Future.delayed(const Duration(milliseconds: 100), () {
                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                    onVisible: () {
                      // Ensure keyboard stays dismissed when snackbar appears
                      _clearFocus();
                    },
                  ),
                );
              });
            }
          },
          child: GestureDetector(
            onTap: _clearFocus,
            behavior: HitTestBehavior.opaque,
            child: Stack(
              children: [
                Container(
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(APP_BG),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SafeArea(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Center(
                              child: Image.asset(
                                APP_ICON,
                                width: 80,
                                height: 75,
                              ),
                            ),
                            const SizedBox(height: SPACE25),
                            Text(
                              'Complete Profile',
                              style: Theme.of(context).textTheme.displayLarge,
                            ),
                            const SizedBox(height: SPACE15),
                            Text(
                              'We need a few more details to complete your account setup.',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: Colors.white70,
                              ),
                            ),
                            const SizedBox(height: SPACE25),
                            Text(
                              'select_nationality'.tr(),
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: SPACE12),
                            _buildNationalityField(context),
                            if (_countryError != null) ...[
                              const SizedBox(height: 5),
                              Text(
                                _countryError!,
                                style: const TextStyle(
                                    color: Colors.red, fontSize: 12),
                              ),
                            ],
                            const SizedBox(height: SPACE15),
                            Text(
                              'enter_phone'.tr(),
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: SPACE12),
                            _buildMobTextField(),
                            const SizedBox(height: SPACE25),
                            BlocBuilder<AuthBloc, AuthState>(
                              builder: (context, state) {
                                return CustomGradientButton(
                                    height: 55,
                                    onPressed: () => _handleCompleteProfile(context),
                                    label: "Complete Profile");
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Build transparent AppBar with back button
  PreferredSizeWidget _buildTransparentAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        color: Colors.transparent,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              children: [
                // Empty space to maintain layout (no back button since user can't go back)
                const SizedBox(width: 48),
                Expanded(
                  child: Text(
                    'Complete Profile',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNationalityField(BuildContext context) {
    return InkWell(
      onTap: () {
        _clearFocus();
        setState(() {
          _countryError = null; // Clear error when user interacts
        });
        showCountryPicker(
            countryListTheme: const CountryListThemeData(
              backgroundColor: Color(0xFF212CB1),
              margin: EdgeInsets.symmetric(horizontal: 10),
              padding: EdgeInsets.all(10),
            ),
            context: context,
            onSelect: (country) {
              setState(() {
                countryName = country.name;
                _countryError = null;
                countryCode = country.countryCode;
                countryDialCode = country.phoneCode;
              });
            });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: _countryError != null
              ? Border.all(color: Colors.red, width: 1)
              : null,
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                countryName.isEmpty ? 'select_nationality'.tr() : countryName,
                style: TextStyle(
                  color: countryName.isEmpty ? Colors.grey[600] : Colors.black,
                  fontSize: 16,
                ),
              ),
            ),
            const Icon(
              Icons.keyboard_arrow_down,
              color: Colors.grey,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: _phoneController.text.isNotEmpty &&
                    _validatePhone(_phoneController.text) != null
                ? Border.all(color: Colors.red, width: 1)
                : null,
          ),
          child: TextFormField(
            controller: _phoneController,
            focusNode: _phoneFocusNode,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              hintText: 'enter_phone'.tr(),
              hintStyle: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 18,
              ),
              prefixIcon: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      countryDialCode,
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 1,
                      height: 20,
                      color: Colors.grey[300],
                    ),
                    const SizedBox(width: 8),
                  ],
                ),
              ),
            ),
            validator: _validatePhone,
            onChanged: (value) {
              setState(() {}); // Trigger rebuild to update border color
            },
          ),
        ),
      ],
    );
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your phone number';
    }
    if (value.length < 8) {
      return 'Phone number must be at least 8 digits';
    }
    return null;
  }

  void _handleCompleteProfile(BuildContext context) {
    // Clear focus first
    _clearFocus();

    // Validate nationality selection
    if (countryName.isEmpty) {
      setState(() {
        _countryError = 'Please select your nationality';
      });
      return;
    }

    // Validate form
    if (_formKey.currentState!.validate()) {
      // Trigger the complete profile event
      context.read<AuthBloc>().add(
        CompleteGoogleProfileRequested(
          tempUserId: widget.tempUserId,
          mobile: '$countryDialCode${_phoneController.text.trim()}',
          nationality: countryName,
        ),
      );
    }
  }
}
