import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:ui' as ui;

import '../../../bloc/forgot_password/forgot_password_bloc.dart';
import '../../../bloc/language/language_bloc.dart';
import '../../core/constants.dart';
import '../../widgets/custom_gradient_button.dart';
import 'email_check_screen.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final TextEditingController _emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final FocusNode _emailFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _emailFieldKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // Add listener to focus node to scroll when field gets focus
    _emailFocusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _emailFocusNode.removeListener(_onFocusChange);
    _emailFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (_emailFocusNode.hasFocus) {
      _scrollToEmailField();
    }
  }

  void _clearFocus() {
    _emailFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  void _scrollToEmailField() {
    // Use a post-frame callback to ensure the layout is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients &&
          _emailFieldKey.currentContext != null) {
        // Get the position of the email field
        final RenderBox renderBox =
            _emailFieldKey.currentContext!.findRenderObject() as RenderBox;
        final position = renderBox.localToGlobal(Offset.zero);

        // Calculate the target scroll position
        // Adjust the offset to position the field where you want it
        final targetPosition = position.dy - 200; // Adjust this value as needed

        _scrollController.animateTo(
          targetPosition,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgotPasswordBloc(),
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: BlocListener<ForgotPasswordBloc, ForgotPasswordState>(
          listener: (context, state) {
            if (state is ForgotPasswordLoading) {
              // Show loading overlay dialog
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            } else if (state is ForgotPasswordSuccess) {
              // Close loading dialog if open
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }

              // Navigate to email check screen
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => EmailCheckScreen(
                    email: state.email,
                  ),
                ),
              );
            } else if (state is ForgotPasswordError) {
              // Close loading dialog if open
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }

              _clearFocus();

              // Use a slight delay to ensure focus is cleared before showing the snackbar
              Future.delayed(const Duration(milliseconds: 100), () {
                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                    onVisible: () {
                      // Ensure keyboard stays dismissed when snackbar appears
                      _clearFocus();
                    },
                  ),
                );
              });
            }
          },
          child: GestureDetector(
            onTap: _clearFocus,
            behavior: HitTestBehavior.opaque,
            child: Stack(
              children: [
                // Background image
                Container(
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(APP_BG),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

                // Foreground content with scroll
                SafeArea(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: MediaQuery.of(context).size.height -
                            MediaQuery.of(context).padding.top -
                            MediaQuery.of(context).padding.bottom,
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                        ),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 20),

                              // Back button
                              IconButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                icon: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 24,
                                ),
                                padding: EdgeInsets.zero,
                                alignment: Alignment.centerLeft,
                              ),

                              const SizedBox(height: 60),

                              // App icon
                              Center(
                                child: Image.asset(
                                  APP_ICON,
                                  width: 80,
                                  height: 75,
                                ),
                              ),

                              const SizedBox(height: 150),

                              // Title
                              Center(
                                child: Text(
                                  'reset_password'.tr(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .displayLarge
                                      ?.copyWith(
                                        fontSize: 28,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                ),
                              ),

                              const SizedBox(height: 20),

                              // Description
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  child: Text(
                                    'reset_password_description'.tr(),
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: Colors.white.withOpacity(0.8),
                                          fontSize: 16,
                                          height: 1.4,
                                        ),
                                  ),
                                ),
                              ),

                              const SizedBox(height: 40),

                              // Email label
                              Padding(
                                padding: const EdgeInsets.only(left: 4),
                                child: Text(
                                  'enter_email'.tr(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ),

                              const SizedBox(height: 12),

                              // Email input field
                              Container(
                                key: _emailFieldKey,
                                child: _buildEmailTextField(),
                              ),

                              const SizedBox(height: 40),

                              // Send reset link button
                              BlocBuilder<ForgotPasswordBloc,
                                  ForgotPasswordState>(
                                builder: (context, state) {
                                  return CustomGradientButton(
                                    onPressed: () {
                                      _clearFocus();
                                      if (_formKey.currentState!.validate()) {
                                        // Dispatch forgot password event
                                        context.read<ForgotPasswordBloc>().add(
                                              ForgotPasswordRequested(
                                                email: _emailController.text
                                                    .trim(),
                                              ),
                                            );
                                      }
                                    },
                                    label: 'send'.tr(),
                                    height: 56,
                                  );
                                },
                              ),

                              const SizedBox(height: SPACE15),

                              // Back to login
                              Center(
                                child: TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: Text(
                                    'back_to_login'.tr(),
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.7),
                                      fontSize: 14,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                              ),

                              // Add extra space at the bottom to ensure scrollability
                              SizedBox(
                                  height:
                                      MediaQuery.of(context).viewInsets.bottom >
                                              0
                                          ? 300
                                          : 50),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // Language toggle button
                Positioned(
                  right: 20,
                  top: 50,
                  child: BlocBuilder<LanguageBloc, LanguageState>(
                    builder: (context, state) {
                      return TextButton(
                        onPressed: () {
                          context.read<LanguageBloc>().add(ToggleLanguage());
                          context.setLocale(state.locale.languageCode == 'en'
                              ? const Locale('ar')
                              : const Locale('en'));
                        },
                        child: Text(
                          state.locale.languageCode == 'en' ? 'AR' : 'EN',
                          style: const TextStyle(color: Colors.white),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmailTextField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _emailController,
        focusNode: _emailFocusNode,
        keyboardType: TextInputType.emailAddress,
        textDirection: ui.TextDirection.ltr,
        textAlign: TextAlign.left,
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
          hintText: 'enter_email_hint'.tr(),
          hintStyle: const TextStyle(
            color: Colors.black54,
            fontSize: 16,
          ),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'email_required'.tr();
          }
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
            return 'email_invalid'.tr();
          }
          return null;
        },
        onTap: _scrollToEmailField,
        textInputAction: TextInputAction.done,
        onEditingComplete: () {
          _clearFocus();
          if (_formKey.currentState!.validate()) {
            context.read<ForgotPasswordBloc>().add(
                  ForgotPasswordRequested(
                    email: _emailController.text.trim(),
                  ),
                );
          }
        },
      ),
    );
  }
}
