import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;

import '../../../data/service/auth_service.dart';
import '../../core/constants.dart';
import '../../widgets/custom_gradient_button.dart';
import 'change_password_screen.dart';

class VerificationScreen extends StatefulWidget {
  final String email;

  const VerificationScreen({
    super.key,
    required this.email,
  });

  @override
  State<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  final List<TextEditingController> _controllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _pinFieldsKey = GlobalKey();
  final AuthService _authService = AuthService();
  bool _isLoading = false;

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    _scrollController.dispose();
    super.dispose();
  }

  void _clearFocus() {
    for (var node in _focusNodes) {
      node.unfocus();
    }
    FocusScope.of(context).unfocus();
  }

  // void _scrollToField() {
  //   if (_scrollController.hasClients && _pinFieldsKey.currentContext != null) {
  //     // Get the position of the pin fields
  //     final RenderBox renderBox =
  //         _pinFieldsKey.currentContext!.findRenderObject() as RenderBox;
  //     final position = renderBox.localToGlobal(Offset.zero);

  //     // Calculate the target scroll position
  //     final targetPosition = position.dy - 200; // Adjust this value as needed

  //     _scrollController.animateTo(
  //       targetPosition,
  //       duration: const Duration(milliseconds: 300),
  //       curve: Curves.easeInOut,
  //     );
  //   }
  // }

  void _onPinChanged(String value, int index) {
    if (value.isNotEmpty && index < 5) {
      _focusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }

    // Check if all fields are filled
    if (_controllers.every((controller) => controller.text.isNotEmpty)) {
      _verifyPin();
    }
  }

  Future<void> _verifyPin() async {
    String pin = _controllers.map((controller) => controller.text).join();
    if (pin.length == 6) {
      _clearFocus();

      // Show loading indicator
      setState(() {
        _isLoading = true;
      });

      // Call the API to verify the code
      final response = await _authService.verifyCode(widget.email, pin);

      // Hide loading indicator
      setState(() {
        _isLoading = false;
      });

      if (response.isSuccess) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('verification_successful'.tr()),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to change password screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => ChangePasswordScreen(
              email: widget.email,
              verificationCode: pin,
            ),
          ),
        );
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('please_enter_all_6_digits'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _resendCode() async {
    _clearFocus();

    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    // Call the API to resend the code
    final response = await _authService.forgotPassword(widget.email);

    // Hide loading indicator
    setState(() {
      _isLoading = false;
    });

    if (response.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('verification_code_resent'.tr()),
          backgroundColor: Colors.blue,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(response.errorMessage),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Calculate the available width for pin fields
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - 48; // 24 padding on each side
    final pinFieldWidth =
        (availableWidth - 40) / 6; // 40 for spacing between fields (8 * 5)

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: GestureDetector(
        onTap: _clearFocus,
        behavior: HitTestBehavior.opaque,
        child: Stack(
          children: [
            // Background image
            Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(APP_BG),
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // Foreground content with scroll
            SafeArea(
              child: SingleChildScrollView(
                controller: _scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        MediaQuery.of(context).padding.bottom,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(height: 60),

                        // App icon
                        Center(
                          child: Image.asset(
                            APP_ICON,
                            width: 80,
                            height: 75,
                          ),
                        ),

                        const SizedBox(height: 100),

                        // Title
                        Column(
                          children: [
                            Text(
                              'verification'.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .displayLarge
                                  ?.copyWith(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                              textAlign: TextAlign.center,
                            ),

                            const SizedBox(height: 16),

                            // Description
                            Center(
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                child: Text(
                                  'verification_description'.tr(),
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: Colors.white.withOpacity(0.8),
                                        fontSize: 16,
                                        height: 1.4,
                                      ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 40),

                            // PIN Input Fields
                            Container(
                              key: _pinFieldsKey,
                              child: _buildPinFields(pinFieldWidth),
                            ),

                            const SizedBox(height: 24),

                            // Resend code
                            Center(
                              child: TextButton(
                                onPressed: _isLoading ? null : _resendCode,
                                child: Text(
                                  'resend_code'.tr(),
                                  style: TextStyle(
                                    color: Colors.white
                                        .withOpacity(_isLoading ? 0.5 : 0.7),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 40),

                        // Send Button
                        _isLoading
                            ? const CircularProgressIndicator(
                                color: Colors.white)
                            : CustomGradientButton(
                                onPressed: _verifyPin,
                                label: 'verify'.tr(),
                                height: 56,
                              ),

                        // Add extra space at the bottom to ensure scrollability
                        SizedBox(
                            height: MediaQuery.of(context).viewInsets.bottom > 0
                                ? 300
                                : 50),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinFields(double pinFieldWidth) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 8,
      runSpacing: 10,
      children: List.generate(
        6,
        (index) => _buildPinField(index, pinFieldWidth),
      ),
    );
  }

  Widget _buildPinField(int index, double width) {
    return Container(
      width: width,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _focusNodes[index].hasFocus
              ? const Color(0xFF6366F1)
              : Colors.grey[300]!,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _controllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        textDirection: ui.TextDirection.ltr,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1A1A1A),
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
          contentPadding: EdgeInsets.zero,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) => _onPinChanged(value, index),
        // onTap: _scrollToField,
        enabled: !_isLoading,
      ),
    );
  }
}
