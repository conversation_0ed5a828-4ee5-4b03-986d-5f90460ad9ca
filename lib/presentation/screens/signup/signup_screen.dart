import 'package:arabic_sign_language/bloc/signup/signup_bloc.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/screens/login/login_screen.dart';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:country_picker/country_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:ui' as ui;

class SignupScreen extends StatefulWidget {
  final String email;
  const SignupScreen({super.key, required this.email});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();
  final List<TextEditingController> _otpControllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _otpFocusNodes =
      List.generate(6, (index) => FocusNode());
  final ScrollController _scrollController = ScrollController();

  // Focus nodes
  final _nameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _phoneFocusNode = FocusNode();

  bool _isPasswordVisible = false;

  String countryName = '';
  String countryCode = 'SA';
  String countryDialCode = '+966';

  // Country validation error (not handled by form validation)
  String? _countryError;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();

    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _otpFocusNodes) {
      focusNode.dispose();
    }

    _nameFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _phoneFocusNode.dispose();

    _scrollController.dispose();

    super.dispose();
  }

  void _clearFocus() {
    _nameFocusNode.unfocus();
    _emailFocusNode.unfocus();
    _passwordFocusNode.unfocus();
    _phoneFocusNode.unfocus();
    for (var focusNode in _otpFocusNodes) {
      focusNode.unfocus();
    }
    FocusScope.of(context).unfocus();
  }

  void _scrollToField(double offset) {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          offset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SignupBloc(),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: _buildTransparentAppBar(),
        resizeToAvoidBottomInset: false,
        body: BlocListener<SignupBloc, SignupState>(
          listener: (context, state) {
            if (state is SignupLoading) {
              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            } else if (state is SignupSuccess) {
              // Close loading dialog if open
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }

              // Show success dialog
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return AlertDialog(
                    backgroundColor: const Color(0xFF212CB1),
                    title: const Text('Success'),
                    content: Text(state.message),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop(); // Close dialog
                          Navigator.of(context).pushReplacement(
                            MaterialPageRoute(
                                builder: (context) => const LoginScreen()),
                          );
                        },
                        child: Text('OK'),
                      ),
                    ],
                  );
                },
              );
            } else if (state is SignupError) {
              // Close loading dialog if open
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }

              _clearFocus();

              // Use a slight delay to ensure focus is cleared before showing the snackbar
              Future.delayed(const Duration(milliseconds: 100), () {
                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                    onVisible: () {
                      // Ensure keyboard stays dismissed when snackbar appears
                      _clearFocus();
                    },
                  ),
                );
              });
            }
          },
          child: GestureDetector(
            onTap: _clearFocus,
            behavior: HitTestBehavior.opaque,
            child: Stack(
              children: [
                Container(
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(APP_BG),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SafeArea(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Center(
                              child: Image.asset(
                                APP_ICON,
                                width: 80,
                                height: 75,
                              ),
                            ),
                            const SizedBox(height: SPACE25),
                            Text(
                              'sign_up'.tr(),
                              style: Theme.of(context).textTheme.displayLarge,
                            ),
                            const SizedBox(height: SPACE15),
                            Text(
                              'enter_name'.tr(),
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: SPACE12),
                            _buildNameField(),
                            // const SizedBox(height: SPACE15),
                            // Text(
                            //   'enter_email'.tr(),
                            //   style: Theme.of(context).textTheme.headlineMedium,
                            // ),
                            // const SizedBox(height: SPACE12),
                            // _buildEmailField(),
                            const SizedBox(height: SPACE15),
                            Text(
                              'enter_password'.tr(),
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: SPACE12),
                            _buildPasswordField(),
                            const SizedBox(height: SPACE15),
                            Text(
                              'select_nationality'.tr(),
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: SPACE12),
                            _buildNationalityField(context),
                            if (_countryError != null) ...[
                              const SizedBox(height: 5),
                              Text(
                                _countryError!,
                                style: const TextStyle(
                                    color: Colors.red, fontSize: 12),
                              ),
                            ],
                            const SizedBox(height: SPACE15),
                            Text(
                              'enter_phone'.tr(),
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: SPACE12),
                            _buildMobTextField(),
                            const SizedBox(height: SPACE15),
                            Text(
                              'Enter Verification Code',
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const SizedBox(height: SPACE12),
                            _buildOtpField(),
                            const SizedBox(height: SPACE25),
                            BlocBuilder<SignupBloc, SignupState>(
                              builder: (context, state) {
                                return CustomGradientButton(
                                    height: 55,
                                    onPressed: () => _handleSignup(context),
                                    label: "create_account".tr());
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Build transparent AppBar with back button
  PreferredSizeWidget _buildTransparentAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        color: Colors.transparent,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 28,
                  ),
                  onPressed: () {
                    _clearFocus();
                    Navigator.of(context).pop();
                  },
                ),
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Validation methods
  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }
    if (value.trim().length < 8) {
      return 'Phone number must be at least 8 digits';
    }
    if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
      return 'Phone number can only contain digits';
    }
    return null;
  }

  // Update form field builders to use localized strings
  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: _nameController.text.isNotEmpty &&
                    _validateName(_nameController.text) != null
                ? Border.all(color: Colors.red, width: 1)
                : null,
          ),
          child: TextFormField(
            controller: _nameController,
            focusNode: _nameFocusNode,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              hintText: 'enter_name'.tr(),
              hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),
              border: InputBorder.none,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
            validator: _validateName,
            textInputAction: TextInputAction.next,
            onChanged: (value) {
              setState(() {}); // Trigger rebuild to update border color
            },
            onEditingComplete: () {
              FocusScope.of(context).requestFocus(_emailFocusNode);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: _emailController.text.isNotEmpty &&
                    _validateEmail(_emailController.text) != null
                ? Border.all(color: Colors.red, width: 1)
                : null,
          ),
          child: TextFormField(
            controller: _emailController,
            focusNode: _emailFocusNode,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              hintText: 'enter_email'.tr(),
              hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),
              border: InputBorder.none,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
            validator: _validateEmail,
            textInputAction: TextInputAction.next,
            onChanged: (value) {
              setState(() {}); // Trigger rebuild to update border color
            },
            onEditingComplete: () {
              FocusScope.of(context).requestFocus(_passwordFocusNode);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: _passwordController.text.isNotEmpty &&
                    _validatePassword(_passwordController.text) != null
                ? Border.all(color: Colors.red, width: 1)
                : null,
          ),
          child: TextFormField(
            controller: _passwordController,
            focusNode: _passwordFocusNode,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
            obscureText: !_isPasswordVisible,
            decoration: InputDecoration(
              hintText: 'enter_password'.tr(),
              hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),
              border: InputBorder.none,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                  color: Colors.grey,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
            ),
            validator: _validatePassword,
            textInputAction: TextInputAction.next,
            onChanged: (value) {
              setState(() {}); // Trigger rebuild to update border color
            },
            onEditingComplete: () {
              FocusScope.of(context).requestFocus(_phoneFocusNode);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNationalityField(BuildContext context) {
    return InkWell(
      onTap: () {
        _clearFocus();
        setState(() {
          _countryError = null; // Clear error when user interacts
        });
        showCountryPicker(
            countryListTheme: const CountryListThemeData(
              backgroundColor: Color(0xFF212CB1),
              margin: EdgeInsets.symmetric(horizontal: 10),
              padding: EdgeInsets.all(10),
            ),
            context: context,
            onSelect: (country) {
              setState(() {
                countryName = country.name;
                _countryError = null;
                countryCode = country.countryCode;
                countryDialCode = country.phoneCode;
              });
            });
      },
      child: Container(
        height: 50,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: _countryError != null
              ? Border.all(color: Colors.red, width: 1)
              : null,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              countryName.isEmpty ? 'select_country'.tr() : countryName,
              style: TextStyle(
                color: countryName.isEmpty ? Colors.grey : Colors.black,
              ),
            ),
            const Icon(
              Icons.arrow_drop_down,
              color: Colors.black,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildOtpField() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(
          6,
          (index) => Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: _buildOtpPinField(index),
                ),
              )),
    );
  }

  Widget _buildOtpPinField(int index) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _otpFocusNodes[index].hasFocus
              ? const Color(0xFF6366F1)
              : Colors.grey[300]!,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _otpControllers[index],
        focusNode: _otpFocusNodes[index],
        textAlign: TextAlign.center,
        textDirection: ui.TextDirection.ltr,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1A1A1A),
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
          contentPadding: EdgeInsets.zero,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) => _onOtpChanged(value, index),
        onTap: () {
          // Scroll to OTP fields when tapped
          _scrollToField(500.0); // Adjust offset as needed
        },
      ),
    );
  }

  Widget _buildMobTextField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: _phoneController.text.isNotEmpty &&
                    _validatePhone(_phoneController.text) != null
                ? Border.all(color: Colors.red, width: 1)
                : null,
          ),
          child: TextFormField(
            controller: _phoneController,
            focusNode: _phoneFocusNode,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              hintText: 'phone_number'.tr(),
              hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(vertical: 20),
              prefixIcon: CountryCodePicker(
                dialogBackgroundColor: const Color(0xFF212CB1),
                showDropDownButton: true,
                textStyle: const TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
                initialSelection: countryCode,
                onChanged: (value) {
                  setState(() {
                    countryCode = value.dialCode ?? countryCode;
                    countryDialCode = value.dialCode ?? countryDialCode;
                  });
                },
                searchDecoration: InputDecoration(
                  labelText: 'search_country'.tr(),
                ),
              ),
            ),
            validator: _validatePhone,
            textInputAction: TextInputAction.done,
            onChanged: (value) {
              setState(() {}); // Trigger rebuild to update border color
            },
            onTap: () {
              // Scroll to mobile field when tapped
              _scrollToField(400.0); // Adjust offset as needed
            },
            onEditingComplete: () {
              _clearFocus();
              if (_formKey.currentState!.validate()) {
                _handleSignup(context);
              }
            },
          ),
        ),
      ],
    );
  }

  // Handle form submission
  void _handleSignup(BuildContext context) {
    _clearFocus();

    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate country selection
    if (countryName.isEmpty) {
      setState(() {
        _countryError = 'Please select your nationality';
      });
      return;
    }

    // Dispatch signup event to bloc
    context.read<SignupBloc>().add(
          SignupRequested(
            name: _nameController.text.trim(),
            email: widget.email,
            password: _passwordController.text,
            mobile: "+$countryDialCode${_phoneController.text.trim()}",
            nationality: countryName,
            verificationCode:
                _otpControllers.map((c) => c.text).join('').toString(),
          ),
        );
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty && index < 5) {
      _otpFocusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _otpFocusNodes[index - 1].requestFocus();
    }
  }
}
