import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/transparent_bottom_navbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:arabic_sign_language/bloc/auth/auth_bloc.dart';
import 'package:arabic_sign_language/presentation/screens/login/login_screen.dart';

ValueNotifier<int> bottomBarIndex = ValueNotifier<int>(0);
ValueNotifier<bool> showMorePopup = ValueNotifier<bool>(false);
ValueNotifier<String> viewMode = ValueNotifier<String>('list');

// Dynamic navigation items
class NavigationItem {
  final String id;
  final String title;
  final String image;
  final IconData icon;
  final Widget screen;
  final VoidCallback? onTap;

  NavigationItem({
    required this.id,
    required this.title,
    required this.image,
    required this.icon,
    required this.screen,
    this.onTap,
  });
}

// All possible navigation items (including defaults and additional)
final List<NavigationItem> _allNavigationItems = [
  // Default items
  NavigationItem(
    id: 'home',
    title: 'home',
    image: IC_HOME,
    icon: Icons.home_rounded,
    screen: const DashboardScreen(),
  ),
  NavigationItem(
    id: 'text',
    title: 'text',
    image: IC_TEXT,
    icon: Icons.textsms_rounded,
    screen: const TextTranscriptScreen(),
  ),
  NavigationItem(
    id: 'speech',
    title: 'speech',
    image: IC_SPEECH,
    icon: Icons.mic_rounded,
    screen: const SpeechTranscriptScreen(),
  ),
  NavigationItem(
    id: 'video',
    title: 'video',
    image: IC_VIDEO,
    icon: Icons.videocam_rounded,
    screen: const VideoTranscriptScreen(),
  ),
  // Additional items
  NavigationItem(
    id: 'dictionary',
    title: 'dictionary',
    image: IC_DICTIONARY,
    icon: Icons.menu_book_rounded,
    screen: const DictionaryScreen(),
  ),
  NavigationItem(
    id: 'sign_to_text',
    title: 'Sign to text',
    image: IC_TEXT, // You can add a specific icon for this
    icon: Icons.sign_language_rounded,
    screen: const DashboardScreen(), // Placeholder screen
  ),
  NavigationItem(
    id: 'settings',
    title: 'settings',
    image: IC_MORE,
    icon: Icons.settings_rounded,
    screen: const DashboardScreen(), // Placeholder screen
  ),
  NavigationItem(
    id: 'profile',
    title: 'Profile',
    image: IC_MORE,
    icon: Icons.person_rounded,
    screen: const DashboardScreen(), // Placeholder screen
  ),
  NavigationItem(
    id: 'subscription',
    title: 'Subscription',
    image: IC_MORE,
    icon: Icons.card_membership_rounded,
    screen: const DashboardScreen(), // Placeholder screen
  ),
  NavigationItem(
    id: 'help',
    title: 'Help',
    image: IC_MORE,
    icon: Icons.help_rounded,
    screen: const DashboardScreen(), // Placeholder screen
  ),
  NavigationItem(
    id: 'about',
    title: 'About',
    image: IC_MORE,
    icon: Icons.info_rounded,
    screen: const DashboardScreen(), // Placeholder screen
    onTap: () {}, // We'll implement this in the _showAboutDialog method
  ),
];

// Current navigation configuration
ValueNotifier<List<NavigationItem>> currentNavItems =
    ValueNotifier<List<NavigationItem>>([
  ..._allNavigationItems
      .where((item) => ['home', 'text', 'speech', 'video'].contains(item.id))
      .toList(),
]);

ValueNotifier<bool> isCustomizationMode = ValueNotifier<bool>(false);
ValueNotifier<bool> isDragMode = ValueNotifier<bool>(false);
ValueNotifier<bool> showCategorizedView = ValueNotifier<bool>(false);

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _loadNavigationItems();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: bottomBarIndex,
      builder: (ctx, index, _) => ValueListenableBuilder(
        valueListenable: currentNavItems,
        builder: (context, navItems, _) => Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              // Main content
              navItems.isNotEmpty && bottomBarIndex.value < navItems.length
                  ? navItems[bottomBarIndex.value].screen
                  : navItems.isNotEmpty
                      ? navItems[0].screen
                      : const DashboardScreen(),

              // Bottom Navigation Bar
              Positioned(
                bottom: 26,
                left: 0,
                right: 0,
                child: SizedBox(
                  height: 70,
                  width: MediaQuery.of(context).size.width,
                  child: TransparentBottomNavBar(
                    currentIndex: bottomBarIndex.value,
                    onTap: (index) {
                      if (index < currentNavItems.value.length) {
                        bottomBarIndex.value = index;
                        showMorePopup.value =
                            false; // Hide popup when switching tabs
                      }
                    },
                    onMoreTap: () {
                      showMorePopup.value = !showMorePopup.value;
                    },
                    onLongPress: () {
                      _showCustomizationDialog(context);
                    },
                    onItemLongPress: (index) {
                      _showRemoveItemDialog(context, index);
                    },
                  ),
                ),
              ),

              // More popup overlay
              ValueListenableBuilder<bool>(
                valueListenable: showMorePopup,
                builder: (context, showPopup, child) {
                  if (!showPopup) return const SizedBox.shrink();

                  return _buildMorePopupOverlay(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMorePopupOverlay(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isCustomizationMode,
      builder: (context, customMode, _) {
        return GestureDetector(
          onTap: customMode
              ? null
              : () {
                  showMorePopup.value = false;
                },
          child: Container(
            color: Colors.black.withOpacity(0.3),
            child: Stack(
              children: [
                // Positioned popup above bottom bar
                Positioned(
                  bottom: 100, // Above the bottom navigation bar
                  left: 16,
                  right: 16,
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF6B46C1).withOpacity(0.95),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 20,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header with view mode toggle
                        ValueListenableBuilder(
                          valueListenable: showCategorizedView,
                          builder: (context, categorizedView, _) {
                            return ValueListenableBuilder(
                              valueListenable: isCustomizationMode,
                              builder: (context, customMode, _) {
                                return Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        categorizedView
                                            ? 'Organize Navigation'
                                            : customMode
                                                ? 'Customize Navigation'
                                                : 'more'.tr(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          fontFamily: FONT_FAMILY,
                                        ),
                                      ),
                                      Row(
                                        children: [
                                          if (!customMode && !categorizedView)
                                            IconButton(
                                              onPressed: () {
                                                showCategorizedView.value =
                                                    true;
                                                isDragMode.value = true;
                                              },
                                              icon: const Icon(
                                                Icons.reorder_rounded,
                                                color: Colors.white,
                                                size: 24,
                                              ),
                                            ),
                                          if (categorizedView || customMode)
                                            IconButton(
                                              onPressed: () {
                                                showCategorizedView.value =
                                                    false;
                                                isDragMode.value = false;
                                                isCustomizationMode.value =
                                                    false;
                                                showMorePopup.value = false;
                                              },
                                              icon: const Icon(
                                                Icons.close_rounded,
                                                color: Colors.white,
                                                size: 24,
                                              ),
                                            )
                                          else
                                            ValueListenableBuilder<String>(
                                              valueListenable: viewMode,
                                              builder: (context, mode, child) {
                                                return IconButton(
                                                  onPressed: () {
                                                    viewMode.value =
                                                        mode == 'list'
                                                            ? 'tile'
                                                            : 'list';
                                                  },
                                                  icon: Icon(
                                                    mode == 'list'
                                                        ? Icons.grid_view
                                                        : Icons.view_list,
                                                    color: Colors.white,
                                                    size: 24,
                                                  ),
                                                );
                                              },
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),

                        // Content based on view mode
                        ValueListenableBuilder(
                          valueListenable: showCategorizedView,
                          builder: (context, categorizedView, _) {
                            if (categorizedView) {
                              return _buildCategorizedDragView(context);
                            } else {
                              return ValueListenableBuilder<String>(
                                valueListenable: viewMode,
                                builder: (context, mode, child) {
                                  return mode == 'list'
                                      ? _buildListViewContent(context)
                                      : _buildTileViewContent(context);
                                },
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildListViewContent(BuildContext context) {
    final availableItems = _getAvailableItems();

    return ValueListenableBuilder(
      valueListenable: isCustomizationMode,
      builder: (context, customMode, _) {
        return Container(
          constraints: const BoxConstraints(maxHeight: 400),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            itemCount: availableItems.length,
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            itemBuilder: (context, index) {
              final item = availableItems[index];
              return GestureDetector(
                onTap: () {
                  if (customMode) {
                    _addToNavigation(item, context);
                  } else {
                    if (item.id == 'dictionary') {
                      _navigateToDictionary(context);
                    } else if (item.id == 'about') {
                      _showAboutDialog(context);
                    } else {
                      _showComingSoon(context, item.title);
                    }
                  }
                },
                onLongPress: customMode
                    ? null
                    : () {
                        // Show feedback and add to navigation
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${item.title} added to navigation!'),
                            backgroundColor: Colors.green,
                            duration: const Duration(seconds: 1),
                          ),
                        );
                        _addToNavigation(item, context);
                      },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(customMode ? 0.15 : 0.08),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(customMode ? 0.3 : 0.15),
                      width: customMode ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          item.icon,
                          color: Colors.white,
                          size: 22,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.title.tr(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                fontFamily: FONT_FAMILY,
                              ),
                            ),
                            if (customMode)
                              Text(
                                'Tap to add to navigation',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 12,
                                  fontFamily: FONT_FAMILY,
                                ),
                              ),
                          ],
                        ),
                      ),
                      Icon(
                        customMode
                            ? Icons.add_circle_outline
                            : Icons.arrow_forward_ios_rounded,
                        color: Colors.white.withOpacity(0.6),
                        size: customMode ? 24 : 16,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildTileViewContent(BuildContext context) {
    final availableItems = _getAvailableItems();

    return ValueListenableBuilder(
      valueListenable: isCustomizationMode,
      builder: (context, customMode, _) {
        return Container(
          constraints: const BoxConstraints(maxHeight: 350),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.6,
            ),
            itemCount: availableItems.length,
            itemBuilder: (context, index) {
              final item = availableItems[index];
              return GestureDetector(
                onTap: () {
                  if (customMode) {
                    _addToNavigation(item, context);
                  } else {
                    if (item.id == 'dictionary') {
                      _navigateToDictionary(context);
                    } else if (item.id == 'about') {
                      _showAboutDialog(context);
                    } else {
                      _showComingSoon(context, item.title);
                    }
                  }
                },
                onLongPress: customMode
                    ? null
                    : () {
                        // Show feedback and add to navigation
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${item.title} added to navigation!'),
                            backgroundColor: Colors.green,
                            duration: const Duration(seconds: 1),
                          ),
                        );
                        _addToNavigation(item, context);
                      },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(customMode ? 0.15 : 0.08),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(customMode ? 0.3 : 0.15),
                      width: customMode ? 2 : 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Stack(
                        children: [
                          Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              item.icon,
                              color: Colors.white,
                              size: 26,
                            ),
                          ),
                          if (customMode)
                            Positioned(
                              top: -2,
                              right: -2,
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: const BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.add,
                                  color: Colors.white,
                                  size: 14,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        item.title.tr(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          fontFamily: FONT_FAMILY,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildCategorizedDragView(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 500),
      child: Column(
        children: [
          // Current Navigation Items Section
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.navigation_rounded,
                      color: Colors.white.withOpacity(0.8),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Current Navigation (${currentNavItems.value.length}/4)',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        fontFamily: FONT_FAMILY,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ValueListenableBuilder(
                  valueListenable: currentNavItems,
                  builder: (context, navItems, _) {
                    return ReorderableListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: navItems.length,
                      onReorder: (oldIndex, newIndex) {
                        _reorderNavigationItems(oldIndex, newIndex);
                      },
                      itemBuilder: (context, index) {
                        final item = navItems[index];
                        return _buildDraggableNavigationItem(item, index, true);
                      },
                    );
                  },
                ),
              ],
            ),
          ),

          // Divider
          Container(
            height: 1,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            color: Colors.white.withOpacity(0.2),
          ),

          // Available Items Section
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.apps_rounded,
                        color: Colors.white.withOpacity(0.8),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Available Items',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          fontFamily: FONT_FAMILY,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                        childAspectRatio: 2.5,
                      ),
                      itemCount: _getAvailableItems().length,
                      itemBuilder: (context, index) {
                        final item = _getAvailableItems()[index];
                        return _buildDraggableNavigationItem(
                            item, index, false);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDraggableNavigationItem(
      NavigationItem item, int index, bool isInNavigation) {
    return Container(
      key: ValueKey(item.id),
      margin: const EdgeInsets.only(bottom: 8),
      child: DragTarget<NavigationItem>(
        onAcceptWithDetails: (details) {
          if (isInNavigation) {
            // Replace this navigation item with the dragged item
            _replaceNavigationItemByDrag(index, details.data);
          }
        },
        builder: (context, candidateData, rejectedData) {
          final isHighlighted = candidateData.isNotEmpty;
          return Draggable<NavigationItem>(
            data: item,
            feedback: Material(
              color: Colors.transparent,
              child: Container(
                width: 200,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(item.icon, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        item.title.tr(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          fontFamily: FONT_FAMILY,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            childWhenDragging: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  style: BorderStyle.solid,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    item.icon,
                    color: Colors.white.withOpacity(0.5),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item.title.tr(),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.5),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: FONT_FAMILY,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (isInNavigation)
                    Icon(
                      Icons.drag_handle,
                      color: Colors.white.withOpacity(0.5),
                      size: 16,
                    ),
                ],
              ),
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: isHighlighted
                    ? Colors.green.withOpacity(0.2)
                    : Colors.white.withOpacity(isInNavigation ? 0.15 : 0.08),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isHighlighted
                      ? Colors.green
                      : Colors.white.withOpacity(isInNavigation ? 0.3 : 0.15),
                  width: isHighlighted ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    item.icon,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item.title.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: FONT_FAMILY,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (isInNavigation)
                    const Icon(
                      Icons.drag_handle,
                      color: Colors.white,
                      size: 16,
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _reorderNavigationItems(int oldIndex, int newIndex) {
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    final items = List<NavigationItem>.from(currentNavItems.value);
    final item = items.removeAt(oldIndex);
    items.insert(newIndex, item);
    currentNavItems.value = items;
    _saveNavigationItems();
  }

  void _replaceNavigationItemByDrag(int index, NavigationItem newItem) {
    final currentItems = List<NavigationItem>.from(currentNavItems.value);
    final replacedItem = currentItems[index];

    // Replace the item in navigation
    currentItems[index] = newItem;
    currentNavItems.value = currentItems;
    _saveNavigationItems();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Replaced ${replacedItem.title} with ${newItem.title}!'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _navigateToDictionary(BuildContext context) {
    showMorePopup.value = false;
    bottomBarIndex.value = 4; // Navigate to dictionary tab
  }

  void _showComingSoon(BuildContext context, String feature) {
    showMorePopup.value = false;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$feature screen coming soon')),
    );
  }

  void _showCustomizationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Customize Navigation'),
        content: const Text(
            'Long press items in the More popup to add them to navigation. If navigation is full, you can choose which item to replace. In customization mode, long press navigation items to remove them.'),
        actions: [
          TextButton(
            onPressed: () {
              isCustomizationMode.value = false;
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              isCustomizationMode.value = true;
              Navigator.of(context).pop();
              showMorePopup.value = true;
            },
            child: const Text('Start Customizing'),
          ),
        ],
      ),
    );
  }

  void _addToNavigation(NavigationItem item, BuildContext context) {
    final currentItems = List<NavigationItem>.from(currentNavItems.value);

    // Check if item is already in navigation
    if (currentItems.any((nav) => nav.id == item.id)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${item.title} is already in navigation'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    // If we have space, just add it
    if (currentItems.length < 4) {
      currentItems.add(item);
      currentNavItems.value = currentItems;
      _saveNavigationItems(); // Save to preferences

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${item.title} added to navigation!'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 1),
        ),
      );
    } else {
      // If navigation is full, show replacement dialog
      _showReplacementDialog(context, item);
      return;
    }

    // Only close popup if not in customization mode
    if (!isCustomizationMode.value) {
      showMorePopup.value = false;
    }
  }

  void _showReplacementDialog(BuildContext context, NavigationItem newItem) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Replace Navigation Item'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
                'Navigation bar is full. Which item would you like to replace with "${newItem.title}"?'),
            const SizedBox(height: 16),
            ...currentNavItems.value.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return ListTile(
                leading: Icon(item.icon),
                title: Text(item.title.tr()),
                trailing: const Icon(Icons.arrow_forward),
                onTap: () {
                  _replaceNavigationItem(index, newItem, context);
                  Navigator.of(context).pop();
                },
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _replaceNavigationItem(
      int index, NavigationItem newItem, BuildContext context) {
    final currentItems = List<NavigationItem>.from(currentNavItems.value);
    final replacedItem = currentItems[index];

    // Replace the item in navigation
    currentItems[index] = newItem;
    currentNavItems.value = currentItems;
    _saveNavigationItems(); // Save to preferences

    // Add the replaced item back to available items
    // We need to update the available items list to include the replaced item
    // This will automatically show up in the popup since _getAvailableItems()
    // filters based on current navigation items

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Replaced ${replacedItem.title} with ${newItem.title}!\n${replacedItem.title} is now available in More menu.'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
      ),
    );

    // Only close popup if not in customization mode
    if (!isCustomizationMode.value) {
      showMorePopup.value = false;
    }
  }

  void _showRemoveItemDialog(BuildContext context, int index) {
    if (index >= currentNavItems.value.length) return;

    final item = currentNavItems.value[index];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Navigation Item'),
        content: Text(
            'Are you sure you want to remove "${item.title}" from navigation?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _removeFromNavigation(index, context);
              Navigator.of(context).pop();
            },
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _removeFromNavigation(int index, BuildContext context) {
    if (index < currentNavItems.value.length &&
        currentNavItems.value.length > 1) {
      final currentItems = List<NavigationItem>.from(currentNavItems.value);
      final removedItem = currentItems.removeAt(index);
      currentNavItems.value = currentItems;
      _saveNavigationItems(); // Save to preferences

      // Adjust current index if needed
      if (bottomBarIndex.value >= currentItems.length) {
        bottomBarIndex.value = currentItems.length - 1;
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${removedItem.title} removed from navigation'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
              'Cannot remove - at least one navigation item is required'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  List<NavigationItem> _getAvailableItems() {
    // Get all items that are not currently in navigation
    return _allNavigationItems
        .where((item) => !currentNavItems.value.any((nav) => nav.id == item.id))
        .toList();
  }

  // Save navigation items to SharedPreferences
  Future<void> _saveNavigationItems() async {
    final prefs = await SharedPreferences.getInstance();
    final itemIds = currentNavItems.value.map((item) => item.id).toList();
    await prefs.setStringList(kEYNavigationItems, itemIds);
  }

  // Load navigation items from SharedPreferences
  Future<void> _loadNavigationItems() async {
    final prefs = await SharedPreferences.getInstance();
    final savedItemIds = prefs.getStringList(kEYNavigationItems);

    if (savedItemIds != null && savedItemIds.isNotEmpty) {
      // Convert saved IDs back to NavigationItem objects
      final savedItems = <NavigationItem>[];
      for (final id in savedItemIds) {
        final item = _allNavigationItems.firstWhere(
          (navItem) => navItem.id == id,
          orElse: () =>
              _allNavigationItems.first, // Fallback to first item if not found
        );
        savedItems.add(item);
      }

      // Update current navigation items
      currentNavItems.value = savedItems;

      // Adjust current index if needed
      if (bottomBarIndex.value >= savedItems.length) {
        bottomBarIndex.value = 0;
      }
    }
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('about'.tr()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('app_name'.tr()),
            const SizedBox(height: 8),
            Text('version'.tr() + ': 1.0.0'),
            const SizedBox(height: 16),
            Text('about_description'.tr()),
            const SizedBox(height: 24),
            // Logout button
            InkWell(
              onTap: () {
                Navigator.pop(context); // Close dialog
                _confirmLogout(context);
              },
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(
                    'logout'.tr(),
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('close'.tr()),
          ),
        ],
      ),
    );
  }

  void _confirmLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('confirm_logout'.tr()),
        content: Text('logout_confirmation_message'.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('cancel'.tr()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              _performLogout(context);
            },
            child: Text(
              'logout'.tr(),
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _performLogout(BuildContext context) {
    // Dispatch logout event to auth bloc
    context.read<AuthBloc>().add(LogoutRequested());

    // Navigate to login screen
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
      (route) => false, // Remove all previous routes
    );

    // Reset bottom navigation index
    bottomBarIndex.value = 0;

    // Hide more popup if visible
    showMorePopup.value = false;
  }
}
