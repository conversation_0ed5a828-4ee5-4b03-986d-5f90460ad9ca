import 'dart:convert';

import 'package:arabic_sign_language/bloc/textTranscript/text_transcript_bloc.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';

class TextTranscriptScreen extends StatefulWidget {
  const TextTranscriptScreen({super.key});

  @override
  State<TextTranscriptScreen> createState() => _TextTranscriptScreenState();
}

class _TextTranscriptScreenState extends State<TextTranscriptScreen> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocusNode = FocusNode();
  UnityWidgetController? _unityWidgetController;

  final ValueNotifier<List<String>> currentAnimationWords =
      ValueNotifier<List<String>>([]);
  final ValueNotifier<String> currentAnimationText = ValueNotifier<String>('');

  List<String> currentProcessingTexts = [];
  ValueNotifier<int> currentIndex = ValueNotifier<int>(0);
  List<String> messagesFromUnity = [];
  List<String> currentMessageList = [];

  @override
  void dispose() {
    _textController.dispose();
    _textFocusNode.dispose();
    currentIndex.dispose();
    currentAnimationWords.dispose();
    currentAnimationText.dispose();
    messagesFromUnity.clear();
    super.dispose();
  }

  void onUnityCreated(controller) {
    _unityWidgetController = controller;
  }

  Future<void> sendMessageToUnity(Map<String, dynamic> message) async {
    message['screen'] = "TextTranscriptScreen";

    String jsonString = json.encode(message);
    debugPrint("jsonString =>$jsonString");
    await _unityWidgetController?.postMessage(
        'MEN', 'PlaySignAnim', jsonString);
  }

  Future<void> stopAnimations() async {
    _unityWidgetController?.postMessage('MEN', 'StopAnimations', "");
  }

  Future<void> adjustAnimationSpeed(String value) async {
    await _unityWidgetController?.postMessage(
        "MEN", 'SetAnimationSpeed', value);
  }

  void pushToSecond(String value) {
    currentProcessingTexts.add(value); // Add the value to `second`.

    // Combine and clean up the processing texts
    String combined = currentProcessingTexts.join().replaceAll(' ', '');
    print("Combined value: $combined");

    String currentFirstElement = context
        .read<TextTranscriptBloc>()
        .rootWords[currentIndex.value]
        .replaceAll(',', '')
        .replaceAll(' ', '');
    print("Current first element: $currentFirstElement ");
    print("Current first element: $combined");

    print(
        "Current first element: ${context.read<TextTranscriptBloc>().rootWords} => ${currentIndex.value}");
    print("Current first element: $value => $currentProcessingTexts");
    print("Current index before update: ${currentIndex.value}");

    // Matching logic
    if (combined == currentFirstElement) {
      currentIndex.value += 1; // Move to the next element in `first`.
      print(
          "Current index after update: ${currentIndex.value}, $currentProcessingTexts");
      currentProcessingTexts.clear(); // Clear for the next processing
      print("Current index after update:  => $currentProcessingTexts");
    } else if (combined.length > currentFirstElement.length) {
      print("Warning: Combined value exceeds the current `first` element.");
    } else {
      print("Currently processing index ${currentIndex.value}");
    }
  }

  // Communication from Unity to Flutter
  Future<void> onUnityMessage(message) async {
    if (message.toString().contains('Current playing Animation')) {
      List<String> parts = message.split('&');
      Map<String, String> result = {};
      for (var part in parts) {
        List<String> keyValue =
            part.split('=>').map((str) => str.trim()).toList();
        if (keyValue.length == 2) {
          result[keyValue[0]] = keyValue[1];
        }
      }
      // final data = splitWords(message.toString());
      // String combinedData = result.join('  ');
      if (result["screen"] == "TextTranscriptScreen") {
        print(
            "currentAnimation unityScreen => ${result['Current playing Animation']}");
        context.read<TextTranscriptBloc>().add(UpdateCurrentAnimationText(
            animationText: result['Current playing Animation'] ?? ""));
      }
    } else if (message.toString().contains('switchtoIdle')) {
      Future.delayed(const Duration(seconds: 3), () {
        print("UpdateCurrentAnimationText => 3s");
        context
            .read<TextTranscriptBloc>()
            .add(UpdateCurrentAnimationText(animationText: ""));
        // context.read<UnityScreenBloc>().isAnimating = false;
        // currentIndex.value = 0;
        // context.read<UnityScreenBloc>().rootWords.clear();
      });
    } else if (message.toString().contains("Current Animation")) {
      currentMessageList.add(message.toString().split("=>").last);
      print("startSpeechToTextProcessing =>c$currentMessageList");
      pushToSecond(message.toString().split("=>").last);
      if (messagesFromUnity.length == currentMessageList.length) {
        currentIndex.value = 0;
        messagesFromUnity.clear();
        currentMessageList.clear();
        context.read<TextTranscriptBloc>().rootWords.clear();
        context
            .read<TextTranscriptBloc>()
            .add(UpdateCurrentAnimationText(animationText: ""));
        print(
            "onUnityMessage => ${context.read<TextTranscriptBloc>().speechTexts}");
        context.read<TextTranscriptBloc>().speechTextIndex++;
      }
    }
  }

  List<String> splitWords(String message) {
    const prefix = "Current playing Animation =>";
    if (message.startsWith(prefix)) {
      String words = message.substring(prefix.length).trim();
      return words.split(' ');
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TextTranscriptBloc, TextTranscriptState>(
      listener: (context, state) {
        if (state is TextTranscriptUnityMessage) {
          // Send individual messages to Unity - same as UnityBloc logic

          if (_unityWidgetController != null) {
            messagesFromUnity.add(state.message['root']);
            sendMessageToUnity(state.message);
          }
        } else if (state is TextTranscriptProcessing) {
          // Initialize animation tracking when processing starts
        } else if (state is TextTranscriptSuccess) {
          // Reset animation tracking when completed
        } else if (state is TextTranscriptError) {
          // Handle transcription error
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        } else if (state is AnimationSpeedUpdated) {
          // Update Unity animation speed when the slider value changes
          adjustAnimationSpeed(state.animationSpeed.toString());
        }
      },
      builder: (context, state) {
        final isLoading = state is TextTranscriptLoading || state.isDataLoading;
        final isTranslateButtonDisabled = state.isTranslateButtonDisabled;
        final isTextFieldEnabled = state.isTextFieldEnabled;
        final animationSpeed = state.animationSpeed;
        // Determine if buttons should be disabled based on animation speed
        final isIncreaseDisabled = animationSpeed >= 2.0;
        final isDecreaseDisabled = animationSpeed <= 0.5;

        return Stack(
          children: [
            Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(APP_BG), fit: BoxFit.cover),
              ),
            ),
            Container(
              padding: const EdgeInsets.fromLTRB(0, kToolbarHeight, 0, 0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  _buildHeader(context),
                  const SizedBox(height: 15),

                  // Main content
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    height: MediaQuery.of(context).size.height * 0.65,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          colors: [
                            const Color(0XFF9064FC).withOpacity(0.5),
                            const Color(0XFF1E113A).withOpacity(0.8),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        )),
                    child: Column(
                      children: [
                        const SizedBox(height: 40),

                        // Text input section
                        _buildTextInputSection(isEnabled: isTextFieldEnabled),

                        const SizedBox(height: 30),

                        CustomGradientButton(
                            onPressed: (state.currentAnimation.isNotEmpty)
                                ? () {}
                                : _handleTranslate,
                            label: "Translate"),

                        const SizedBox(height: 30),

                        // Character avatar
                        _buildCharacterAvatar(context),

                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              right: MediaQuery.sizeOf(context).width * 0.09,
              top: MediaQuery.sizeOf(context).height * 0.52,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(23),
                  color: const Color(0XFF49446C).withOpacity(0.2),
                ),
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildControlButton(Icons.refresh, () {
                      context
                          .read<TextTranscriptBloc>()
                          .add(ReplayTranscription());
                    }, const Color(0XFF7B6FFF)),
                    const SizedBox(height: 5),
                    _buildControlButton(
                        Icons.add,
                        isIncreaseDisabled
                            ? () {}
                            : () {
                                // Increase animation speed
                                final newSpeed = animationSpeed + 0.5;
                                context.read<TextTranscriptBloc>().add(
                                      UpdateAnimationSpeed(value: newSpeed),
                                    );
                              },
                        isIncreaseDisabled
                            ? Colors.grey
                                .withOpacity(0.4)
                                .withOpacity(0.5) // Disabled color
                            : const Color(0XFF2D2456)),
                    const SizedBox(height: 5),
                    _buildControlButton(
                        Icons.remove,
                        isDecreaseDisabled
                            ? () {}
                            : () {
                                // Decrease animation speed
                                final newSpeed = animationSpeed - 0.5;
                                context.read<TextTranscriptBloc>().add(
                                      UpdateAnimationSpeed(value: newSpeed),
                                    );
                              },
                        isDecreaseDisabled
                            ? Colors.grey.withOpacity(0.4) // Disabled color
                            : const Color.fromARGB(255, 54, 53, 61)),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: MediaQuery.sizeOf(context).height * 0.12,
              child: // Status text
                  _buildStatusText(state, MediaQuery.sizeOf(context)),
            )
          ],
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          // Back button
          GestureDetector(
              onTap: () {
                stopAnimations();
                context
                    .read<TextTranscriptBloc>()
                    .add(UpdateCurrentAnimationText(animationText: ""));
                bottomBarIndex.value = 0;
              },
              child: Image.asset(BACK_BUTTON, height: 25, width: 25)),

          const SizedBox(width: 15),

          // Title
          const Text(
            'Translate Text',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: FONT_FAMILY,
            ),
          ),

          const Spacer(),

          // Info button
          GestureDetector(
              onTap: () {}, child: Image.asset(INFO, height: 25, width: 25)),
        ],
      ),
    );
  }

  Widget _buildTextInputSection({bool isEnabled = true}) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.15,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: const Color(0XFF221B67),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // Text input field
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextFormField(
              controller: _textController,
              focusNode: _textFocusNode,
              enabled: isEnabled,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: FONT_FAMILY,
              ),
              decoration: InputDecoration(
                hintText: 'Enter your Text',
                hintStyle: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 16,
                  fontFamily: FONT_FAMILY,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),

          // Camera icon positioned at bottom right
          Positioned(
            bottom: 12,
            right: 12,
            child: GestureDetector(
              onTap: () {
                // Handle camera functionality
                _handleCameraInput();
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterAvatar(BuildContext context) {
    return Expanded(
      child: Container(
        height: MediaQuery.sizeOf(context).height * 0.1,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white.withOpacity(0.1),
              Colors.white.withOpacity(0.05),
            ],
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              // Character image
              UnityWidget(
                onUnityCreated: onUnityCreated,
                onUnityMessage: onUnityMessage,
              ),

              // Control buttons
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton(
      IconData icon, VoidCallback onTap, Color backgroundColor) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 45,
        height: 45,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(22.5),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  void _handleTranslate() {
    if (_textController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter text to translate'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Trigger the BLoC event to process transcription
    context.read<TextTranscriptBloc>().add(
          ProcessTextTranscription(
            inputText: _textController.text,
            inputTextController: _textController,
            inputFocusNode: _textFocusNode,
          ),
        );
  }

  void _handleCameraInput() {
    // Handle camera functionality for text recognition
    // This could open camera to capture text or image
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Camera functionality will be implemented'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Animation tracking methods - similar to UnityScreen
  void _initializeAnimationTracking(dynamic state) {
    // Extract all words from the original text for animation tracking
    String fullText = '';
    if (state is TextTranscriptProcessing) {
      fullText = state.originalText;
    } else if (state is TextTranscriptUnityMessage) {
      fullText = state.originalText;
    }

    List<String> words =
        fullText.split(' ').where((word) => word.isNotEmpty).toList();

    currentAnimationText.value = fullText;
    currentAnimationWords.value = words;
    currentIndex.value = -1; // Start with no word highlighted
  }

  void _updateCurrentAnimation(TextTranscriptUnityMessage state) {
    // Find the current word being animated and update the index
    String currentRoot = state.message['root']?.toString() ?? '';
    List<String> words = currentAnimationWords.value;

    // Find the index of the current word being animated
    for (int i = 0; i < words.length; i++) {
      if (words[i].contains(currentRoot) || currentRoot.contains(words[i])) {
        currentIndex.value = i;
        break;
      }
    }
  }

  Widget _currentAnimation(Size size) {
    return BlocBuilder<TextTranscriptBloc, TextTranscriptState>(
      builder: (context, state) {
        List<String> words = state.currentAnimation
            .replaceAll('[', '')
            .replaceAll(']', '')
            .split(', ');
        return SizedBox(
          width: size.width * 0.7,
          child: ValueListenableBuilder(
              valueListenable: currentIndex,
              builder: (context, currentActiveIndex, _) {
                return Text.rich(
                  TextSpan(
                    children: List<TextSpan>.generate(words.length, (index) {
                      return TextSpan(
                        text:
                            '${words[index]}${index < words.length - 1 ? ' ' : ''}', // Add space between words
                        style: TextStyle(
                          color: index == currentActiveIndex
                              ? Colors.red
                              : Colors.black, // Highlighted color
                          fontWeight: index == currentActiveIndex
                              ? FontWeight.bold
                              : FontWeight.normal, // Bold for highlighted
                          fontSize: 18,
                        ),
                      );
                    }),
                  ),
                  textAlign: TextAlign.center,
                );
              }),
        );
      },
    );
  }

  // Current animation widget - similar to UnityScreen
  Widget _buildCurrentAnimation() {
    return ValueListenableBuilder<String>(
      valueListenable: currentAnimationText,
      builder: (context, animationText, child) {
        if (animationText.isEmpty) {
          return const SizedBox.shrink();
        }

        return ValueListenableBuilder<List<String>>(
          valueListenable: currentAnimationWords,
          builder: (context, words, child) {
            if (words.isEmpty) {
              return const SizedBox.shrink();
            }

            return ValueListenableBuilder<int>(
              valueListenable: currentIndex,
              builder: (context, index, child) {
                return Container(
                  width: MediaQuery.of(context).size.width,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: words.asMap().entries.map((entry) {
                      int wordIndex = entry.key;
                      String word = entry.value;
                      bool isCurrentWord = wordIndex == index;

                      return Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4, vertical: 2),
                        child: Text(
                          word,
                          style: TextStyle(
                            color: isCurrentWord ? Colors.red : Colors.white,
                            fontSize: 16,
                            fontWeight: isCurrentWord
                                ? FontWeight.bold
                                : FontWeight.normal,
                            fontFamily: FONT_FAMILY,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildStatusText(TextTranscriptState state, Size size) {
    return Container(
      width: MediaQuery.sizeOf(context).width,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: BlocBuilder<TextTranscriptBloc, TextTranscriptState>(
        builder: (context, state) {
          List<String> words = state.currentAnimation
              .replaceAll('[', '')
              .replaceAll(']', '')
              .split(', ');
          return SizedBox(
            width: size.width * 0.7,
            child: ValueListenableBuilder(
                valueListenable: currentIndex,
                builder: (context, currentActiveIndex, _) {
                  return Text.rich(
                    TextSpan(
                      children: List<TextSpan>.generate(words.length, (index) {
                        return TextSpan(
                          text:
                              '${words[index]}${index < words.length - 1 ? ' ' : ''}', // Add space between words
                          style: TextStyle(
                            backgroundColor: index == currentActiveIndex
                                ? const Color(0XFF755BFF)
                                : null,
                            color: Colors.white, // Highlighted color
                            fontWeight: FontWeight.bold, // Bold for highlighted
                            fontSize: 18,
                          ),
                        );
                      }),
                    ),
                    textAlign: TextAlign.center,
                  );
                }),
          );
        },
      ),
    );
  }
}
