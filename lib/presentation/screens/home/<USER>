import 'dart:convert';

import 'package:arabic_sign_language/bloc/VideoTranscription/video_transcription_bloc.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';

import 'package:flutter/material.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class VideoTranscriptScreen extends StatefulWidget {
  const VideoTranscriptScreen({super.key});

  @override
  State<VideoTranscriptScreen> createState() => _VideoTranscriptScreenState();
}

class _VideoTranscriptScreenState extends State<VideoTranscriptScreen> {
  final TextEditingController _videoLinkController = TextEditingController();
  FocusNode _focusNode = FocusNode();
  bool _isVideoInitialized = false;
  late UnityWidgetController _unityWidgetController;
  YoutubePlayerController? _youtubePlayerController;
  ValueNotifier<int> currentIndex = ValueNotifier<int>(0);
  List<String> messagesFromUnity = [];
  List<String> currentMessageList = [];
  List<String> currentProcessingTexts = [];
  late VideoTranscriptionBloc _videoTranscriptionBloc;

  // Communication from  Flutter to Unity
  Future<void> sendMessageToUnity(Map<String, dynamic> message) async {
    message['screen'] = "VideoTranscriptScreen";
    String jsonString = json.encode(message);
    print("jsonString =>$jsonString");
    await _unityWidgetController.postMessage('MEN', 'PlaySignAnim', jsonString);
  }

  Future<void> stopCurrentAnimation() async {
    context
        .read<VideoTranscriptionBloc>()
        .add(const UpdateCurrentAnimationText(animationText: ""));
    await _unityWidgetController.postMessage("MEN", 'StopAnimations', "");
  }

  void pushToSecond(String value) {
    currentProcessingTexts.add(value); // Add the value to `second`.

    // Remove spaces for processing clarity
    String combined = currentProcessingTexts.join().replaceAll(' ', '');
    String currentFirstElement = context
        .read<VideoTranscriptionBloc>()
        .rootWords[currentIndex.value]
        .replaceAll(',', '')
        .replaceAll(' ', '');

    print("Updated second: $combined , ${currentFirstElement}");

    if (combined == currentFirstElement) {
      // If the combined value matches the current `first` element.
      currentIndex.value++; // Move to the next element in `first`.
      // Clear `second` for the next processing if necessary.
      currentProcessingTexts.clear();
    } else if (combined.length > currentFirstElement.length) {
      // If combined exceeds the current `first` element, log a warning.
      print("Warning: Combined value exceeds the current `first` element.");
    } else {
      print("Currently processing index ${currentIndex.value}");
    }
  }

  // void processMatching(List<String> first, List<String> second) {
  //   int firstIndex = 0;
  //   String buffer = "";

  //   for (String element in second) {
  //     buffer += element; // Add the element to the buffer.
  //     print("Buffer: $buffer"); // Debugging line to see progress.

  //     // Check if the current buffer matches the current element in `first`.
  //     if (buffer == first[firstIndex].replaceAll(',', '')) {
  //       print("Matched: ${first[firstIndex]} at index $firstIndex");
  //       firstIndex++; // Move to the next element in `first`.
  //       buffer = ""; // Clear the buffer after a match.

  //       // Stop if all elements in `first` are processed.
  //       if (firstIndex >= first.length) {
  //         print("Processing complete!");
  //         break;
  //       }
  //     }
  //   }

  //   // Check if any unmatched elements remain in `second`.
  //   if (firstIndex < first.length) {
  //     print("Unprocessed part of first: ${first.sublist(firstIndex)}");
  //   }
  // }

  // Communication from Unity to Flutter
  void onUnityMessage(message) {
    if (message.toString().contains('Current playing Animation')) {
      List<String> parts = message.split('&');
      Map<String, String> result = {};
      for (var part in parts) {
        List<String> keyValue =
            part.split('=>').map((str) => str.trim()).toList();
        if (keyValue.length == 2) {
          result[keyValue[0]] = keyValue[1];
        }
      }
      // final data = splitWords(message.toString());
      // String combinedData = data.join('  ');
      if (result["screen"] == "VideoTranscriptScreen") {
        final currentWord = result['Current playing Animation']!
            .replaceAll("[", "")
            .replaceAll("]", "")
            .split(", ");
        context.read<VideoTranscriptionBloc>().add(
            UpdateCurrentAnimationText(animationText: currentWord.toString()));
      }
    } else if (message.toString().contains('switchtoIdle')) {
      Future.delayed(const Duration(seconds: 3), () {
        print("UpdateCurrentAnimationText => 3s");
        context
            .read<VideoTranscriptionBloc>()
            .add(const UpdateCurrentAnimationText(animationText: ""));
      });
    } else if (message.toString().contains("Current Animation")) {
      currentMessageList.add(message.toString().split("=>").last);
      pushToSecond(message.toString().split("=>").last);
      if (messagesFromUnity.length == currentMessageList.length) {
        if (!context.read<VideoTranscriptionBloc>().isManuallyPaused) {
          _youtubePlayerController?.play();
          currentIndex.value = 0;
          messagesFromUnity.clear();
          currentMessageList.clear();
          context.read<VideoTranscriptionBloc>().rootWords.clear();
        }
      }
    }
  }

  List<String> splitWords(String message) {
    const prefix = "Current playing Animation =>";
    if (message.startsWith(prefix)) {
      String words = message.substring(prefix.length).trim();
      return words.split(' ');
    }
    return [];
  }

  void onUnityCreated(controller) {
    _unityWidgetController = controller;
  }

  void _loadVideo([String? videoUrl]) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final url = videoUrl ?? _videoLinkController.text.trim();
      final videoId = YoutubePlayer.convertUrlToId(url);

      if (videoId != null) {
        // _youtubePlayerController?.dispose(); // dispose previous
        setState(() {
          _youtubePlayerController = YoutubePlayerController(
            initialVideoId: videoId,
            flags: const YoutubePlayerFlags(
              autoPlay: true,
              mute: false,
            ),
          );
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invalid YouTube URL')),
        );
      }
    });
  }

  @override
  void initState() {
    _videoTranscriptionBloc = context.read<VideoTranscriptionBloc>();
    super.initState();
  }

  @override
  void dispose() {
    _videoLinkController.dispose();
    _videoTranscriptionBloc.captionList = [];
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return BlocConsumer<VideoTranscriptionBloc, VideoTranscriptionState>(
      listener: (context, state) {
        print("state from video transcription screen => $state");
        if (state is VideoTranscriptionScreenError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        } else if (state is SendMessagesToVideoScreen) {
          print("state is SendMessagesToVideoScreen");
          print("******${state.message}*****");
          messagesFromUnity.add(state.message['root']);
          sendMessageToUnity(state.message);
        } else if (state is VideoTranscriptionSuccess && state.isDisableInput) {
          // Load video when cached data is available and inputs are disabled
          _loadVideo(state.videoId);
        } else if (state is VideoTranscriptionInitial) {
          // Clear YouTube player when state is reset to initial
          setState(() {
            _youtubePlayerController = null;
          });
        }
      },
      builder: (context, state) {
        return Stack(
          children: [
            Container(
              height: size.height,
              width: size.width,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(APP_BG),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.fromLTRB(0, kToolbarHeight, 0, 0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  _buildHeader(context),
                  const SizedBox(height: 15),
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    // padding: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        gradient: LinearGradient(
                          colors: [
                            const Color(0XFF9064FC).withOpacity(0.5),
                            const Color(0XFF1E113A).withOpacity(0.8),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        )),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 10),

                        // Video Link Input - Hide when isDisableInput is true
                        if (!state.isDisableInput) ...[
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 20),
                            child: Text(
                              'Video Link',
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FONT_FAMILY_INTER),
                            ),
                          ),
                          const SizedBox(height: 10),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2A2157),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: Colors.white.withOpacity(0.1)),
                            ),
                            child: TextField(
                              ignorePointers: state.isDataLoading,
                              controller: _videoLinkController,
                              focusNode: _focusNode,
                              style: const TextStyle(color: Colors.white),
                              decoration: InputDecoration(
                                hintText: 'https://www.youtube.com/watch...',
                                hintStyle: TextStyle(
                                    color: Colors.white.withOpacity(0.5)),
                                border: InputBorder.none,
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: CustomGradientButton(
                              onPressed: () {
                                _focusNode.unfocus();
                                currentIndex.value = 0;
                                messagesFromUnity.clear();
                                currentMessageList.clear();
                                context
                                    .read<VideoTranscriptionBloc>()
                                    .rootWords
                                    .clear();
                                context.read<VideoTranscriptionBloc>().add(
                                    StartVideoTranscription(
                                        videoId:
                                            _videoLinkController.text.trim(),
                                        controller: _videoLinkController));
                              },
                              label: state.isDataLoading
                                  ? "Translating..."
                                  : "Translate",
                              gradientColors: !state.isDataLoading
                                  ? [purpleMimosa, lightRoyalBlue]
                                  : [Colors.grey[900]!, Colors.grey],
                            ),
                          ),
                        ],
                        const SizedBox(height: 20),
                        // Character avatar
                        _buildCharacterAvatar(context),

                        const SizedBox(height: 20),
                        // Status text
                        _buildStatusText(state, MediaQuery.sizeOf(context)),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  if (_youtubePlayerController != null) ...[
                    Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          gradient: LinearGradient(
                            colors: [
                              const Color(0XFF9064FC).withOpacity(0.5),
                              const Color(0XFF1E113A).withOpacity(0.8),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          )),
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      // height: size.height * 0.3,
                      child: _youtubePlayerController != null
                          ? Container(
                              padding: const EdgeInsets.all(10),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(13),
                                child: YoutubePlayer(
                                  bufferIndicator: null,
                                  bottomActions: const [],
                                  controller: _youtubePlayerController!,
                                  onReady: () {
                                    context.read<VideoTranscriptionBloc>().add(
                                          AddVideoListener(
                                              controller:
                                                  _youtubePlayerController!),
                                        );
                                  },
                                ),
                              ),
                            )
                          : const SizedBox(),
                    ),
                    const SizedBox(height: 15),
                    // Toggle button to show/hide input fields
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              stopCurrentAnimation();
                              _youtubePlayerController?.pause();
                              currentIndex.value = 0;
                              messagesFromUnity.clear();
                              currentMessageList.clear();
                              context
                                  .read<VideoTranscriptionBloc>()
                                  .rootWords
                                  .clear();
                              // Toggle input visibility
                              context.read<VideoTranscriptionBloc>().add(
                                    ToggleInputVisibility(
                                        showInput: state.isDisableInput),
                                  );
                              // Also hide YouTube widget when showing inputs
                              if (state.isDisableInput) {
                                setState(() {
                                  _youtubePlayerController = null;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF2A2157),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    state.isDisableInput
                                        ? Icons.keyboard_arrow_up
                                        : Icons.keyboard_arrow_down,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    state.isDisableInput
                                        ? 'Show Input'
                                        : 'Hide Video',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ]
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildControlButton(
      IconData icon, VoidCallback onTap, Color backgroundColor) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 35,
        height: 35,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          // Back button
          GestureDetector(
              onTap: () {
                bottomBarIndex.value = 0;
                _youtubePlayerController?.dispose();
                // Reset VideoTranscription state to default
                context.read<VideoTranscriptionBloc>().add(
                      ResetVideoTranscription(controller: _videoLinkController),
                    );
              },
              child: Image.asset(BACK_BUTTON, height: 25, width: 25)),

          const SizedBox(width: 15),

          // Title
          const Text(
            'Video Translation ',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: FONT_FAMILY,
            ),
          ),

          const Spacer(),

          // Info button
          GestureDetector(
              onTap: () {}, child: Image.asset(INFO, height: 25, width: 25)),
        ],
      ),
    );
  }

  Widget _buildCharacterAvatar(BuildContext context) {
    final screenHeight = MediaQuery.sizeOf(context).height;
    return Container(
      height: screenHeight * 0.25, // Increased height for better proportions
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.1),
            Colors.white.withOpacity(0.05),
          ],
        ),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Character image in ClipRRect for rounded corners
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: UnityWidget(
              onUnityCreated: onUnityCreated,
              onUnityMessage: onUnityMessage,
            ),
          ),

          // Control buttons positioned to the right
          Positioned(
            right: -25, // Position outside the container
            top: screenHeight * 0.05, // Center vertically
            child: BlocBuilder<VideoTranscriptionBloc, VideoTranscriptionState>(
              builder: (context, state) {
                final animationSpeed = state.animationSpeed;
                // Determine if buttons should be disabled based on animation speed
                final isIncreaseDisabled = animationSpeed >= 2.0;
                final isDecreaseDisabled = animationSpeed <= 0.5;
                return Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(23),
                    color: const Color(0XFF49446C).withOpacity(0.7),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildControlButton(
                          Icons.refresh, () {}, const Color(0XFF7B6FFF)),
                      const SizedBox(height: 10),
                      _buildControlButton(
                          Icons.add,
                          isIncreaseDisabled
                              ? () {}
                              : () {
                                  // Increase animation speed
                                  final newSpeed = animationSpeed + 0.5;
                                  context.read<VideoTranscriptionBloc>().add(
                                        UpdateAnimationSpeed(value: newSpeed),
                                      );
                                },
                          isIncreaseDisabled
                              ? Colors.grey
                                  .withOpacity(0.4)
                                  .withOpacity(0.5) // Disabled color
                              : const Color(0XFF2D2456)),
                      const SizedBox(height: 5),
                      _buildControlButton(
                          Icons.remove,
                          isDecreaseDisabled
                              ? () {}
                              : () {
                                  // Decrease animation speed
                                  final newSpeed = animationSpeed - 0.5;
                                  context.read<VideoTranscriptionBloc>().add(
                                        UpdateAnimationSpeed(value: newSpeed),
                                      );
                                },
                          isDecreaseDisabled
                              ? Colors.grey.withOpacity(0.4) // Disabled color
                              : const Color.fromARGB(255, 54, 53, 61)),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusText(VideoTranscriptionState state, Size size) {
    return Container(
      width: MediaQuery.sizeOf(context).width,
      height: 120,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: BlocBuilder<VideoTranscriptionBloc, VideoTranscriptionState>(
        builder: (context, state) {
          List<String> words = state.currentAnimation
              .replaceAll('[', '')
              .replaceAll(']', '')
              .split(', ');
          return SizedBox(
            height: 120,
            width: size.width * 0.7,
            child: ValueListenableBuilder(
                valueListenable: currentIndex,
                builder: (context, currentActiveIndex, _) {
                  return Text.rich(
                    TextSpan(
                      children: List<TextSpan>.generate(words.length, (index) {
                        return TextSpan(
                          text:
                              '${words[index]}${index < words.length - 1 ? ' ' : ''}', // Add space between words
                          style: TextStyle(
                            backgroundColor: index == currentActiveIndex
                                ? const Color(0XFF755BFF)
                                : null,
                            color: Colors.white, // Highlighted color
                            fontWeight: FontWeight.bold, // Bold for highlighted
                            fontSize: 18,
                          ),
                        );
                      }),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 4,
                  );
                }),
          );
        },
      ),
    );
  }
}
