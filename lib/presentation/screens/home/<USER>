import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Stack(
      children: [
        Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image:
                DecorationImage(image: AssetImage(APP_BG), fit: BoxFit.cover),
          ),
        ),
        Container(
          padding: const EdgeInsets.fromLTRB(0, kToolbarHeight, 0, 0),
          height: size.height * 0.9,
          width: size.width,
          // color: Colors.amber,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // Align(
                //     alignment: Alignment.topRight,
                //     child: IconButton(
                //         onPressed: () {}, icon: Icon(Icons.more_vert))),
                Container(
                  height: size.height * 0.24,
                  decoration: BoxDecoration(
                      color: const Color.fromARGB(52, 95, 21, 215)
                          .withOpacity(0.5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                          color: const Color(0xFF71707080), width: 1)),
                  child: Center(
                    child: Image.asset(
                      ARAB_AVATAR,
                      height: 164,
                      width: 164,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                Row(
                  children: [
                    gridItem(size, context, size.width / 2, TRANSLATE_TEXT,
                        "translate_text", "translate_text_desc"),
                    const SizedBox(
                      width: 15,
                    ),
                    gridItem(size, context, size.width / 2, TRANSLATE_SPEECH,
                        "translate_speech", "translate_speech_desc"),
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                Row(
                  children: [
                    gridItem(size, context, size.width / 2, TRANSLATE_VIDEO,
                        "translate_video", "translate_video_desc"),
                    const SizedBox(
                      width: 15,
                    ),
                    gridItem(size, context, size.width / 2, DICTIONARY,
                        "sign_dictionary", "sign_dictionary_desc"),
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                gridItem(
                  size,
                  context,
                  size.width,
                  SIGNTOTEXT,
                  "sign_to_text",
                  "sign_to_text_desc",
                ),
                const SizedBox(
                  height: 15,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget gridItem(Size size, BuildContext context, double width, String image,
      String title, String subtitle) {
    return Expanded(
      child: Container(
        height: size.height * 0.17,
        width: width,
        decoration: BoxDecoration(
          color: const Color.fromARGB(52, 95, 21, 215).withOpacity(0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFF71707080), width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              height: 12,
            ),
            Image.asset(
              image,
              height: 35,
              width: 35,
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              title.tr(),
              style: Theme.of(context).textTheme.displaySmall,
            ),
            const SizedBox(
              height: 10,
            ),
            Flexible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Text(
                  subtitle.tr(),
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
